// src/app/admin/page.jsx
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Sprawdź czy użytkownik jest już zalogowany
    const token = localStorage.getItem('admin-token');
    if (token) {
      verifyToken(token);
    } else {
      setLoading(false);
    }
  }, []);

  const verifyToken = async (token) => {
    try {
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setIsAuthenticated(true);
      } else {
        localStorage.removeItem('admin-token');
      }
    } catch (error) {
      console.error('Token verification failed:', error);
      localStorage.removeItem('admin-token');
    }
    setLoading(false);
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');

    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('admin-token', data.token);
        setIsAuthenticated(true);
      } else {
        setError(data.error || 'Nieprawidłowe hasło');
      }
    } catch (error) {
      setError('Błąd połączenia');
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin-token');
    setIsAuthenticated(false);
    setPassword('');
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"></div>
          <p className="text-temple">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-rice to-mist">
        <div className="bg-white p-8 rounded-2xl shadow-soft max-w-md w-full mx-4">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-serif text-temple mb-2">Panel Administracyjny</h1>
            <p className="text-wood-light">Bakasana Travel Blog</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-temple mb-2">
                Hasło administratora
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-temple/20 rounded-lg focus:ring-2 focus:ring-temple/20 focus:border-temple transition-colors"
                placeholder="Wprowadź hasło"
                required
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <button
              type="submit"
              className="w-full bg-temple text-white py-3 px-4 rounded-lg hover:bg-temple/90 transition-colors font-medium"
            >
              Zaloguj się
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-xs text-wood-light">
              🔒 Zabezpieczone połączenie
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rice to-mist">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-temple/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-serif text-temple">Panel Administracyjny</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-wood-light">Zalogowany jako Administrator</span>
              <button
                onClick={handleLogout}
                className="text-sm text-temple hover:text-temple/70 transition-colors"
              >
                Wyloguj się
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* Sanity CMS */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-temple/10 rounded-lg flex items-center justify-center mr-4">
                <span className="text-2xl">📝</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-temple">Sanity CMS</h3>
                <p className="text-sm text-wood-light">Zarządzaj treścią</p>
              </div>
            </div>
            <p className="text-wood-light text-sm mb-4">
              Edytuj retreaty, opinie, FAQ i artykuły bloga
            </p>
            <a
              href={process.env.NEXT_PUBLIC_SANITY_STUDIO_URL || 'https://bakasana-travel.sanity.studio'}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-temple text-white text-sm rounded-lg hover:bg-temple/90 transition-colors"
            >
              Otwórz CMS
              <span className="ml-2">→</span>
            </a>
          </div>

          {/* Analytics */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-golden/10 rounded-lg flex items-center justify-center mr-4">
                <span className="text-2xl">📊</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-temple">Analytics</h3>
                <p className="text-sm text-wood-light">Statystyki strony</p>
              </div>
            </div>
            <p className="text-wood-light text-sm mb-4">
              Zobacz raporty odwiedzin i konwersji
            </p>
            <a
              href="https://analytics.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-golden text-white text-sm rounded-lg hover:bg-golden/90 transition-colors"
            >
              Google Analytics
              <span className="ml-2">→</span>
            </a>
          </div>

          {/* Bookings */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-sage/10 rounded-lg flex items-center justify-center mr-4">
                <span className="text-2xl">📅</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-temple">Rezerwacje</h3>
                <p className="text-sm text-wood-light">Zarządzaj bookingami</p>
              </div>
            </div>
            <p className="text-wood-light text-sm mb-4">
              Przeglądaj i zarządzaj rezerwacjami
            </p>
            <button
              onClick={() => router.push('/admin/bookings')}
              className="inline-flex items-center px-4 py-2 bg-sage text-white text-sm rounded-lg hover:bg-sage/90 transition-colors"
            >
              Zobacz rezerwacje
              <span className="ml-2">→</span>
            </button>
          </div>

          {/* Newsletter */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-lotus/10 rounded-lg flex items-center justify-center mr-4">
                <span className="text-2xl">📧</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-temple">Newsletter</h3>
                <p className="text-sm text-wood-light">Zarządzaj subskrypcjami</p>
              </div>
            </div>
            <p className="text-wood-light text-sm mb-4">
              Przeglądaj subskrybentów i wysyłaj kampanie
            </p>
            <button
              onClick={() => router.push('/admin/newsletter')}
              className="inline-flex items-center px-4 py-2 bg-lotus text-white text-sm rounded-lg hover:bg-lotus/90 transition-colors"
            >
              Zarządzaj newsletter
              <span className="ml-2">→</span>
            </button>
          </div>

          {/* Settings */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-wood/10 rounded-lg flex items-center justify-center mr-4">
                <span className="text-2xl">⚙️</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-temple">Ustawienia</h3>
                <p className="text-sm text-wood-light">Konfiguracja strony</p>
              </div>
            </div>
            <p className="text-wood-light text-sm mb-4">
              Zarządzaj ustawieniami i konfiguracją
            </p>
            <button
              onClick={() => router.push('/admin/settings')}
              className="inline-flex items-center px-4 py-2 bg-wood text-white text-sm rounded-lg hover:bg-wood/90 transition-colors"
            >
              Ustawienia
              <span className="ml-2">→</span>
            </button>
          </div>

        </div>

        {/* Quick Stats */}
        <div className="mt-8 bg-white rounded-xl shadow-soft p-6">
          <h3 className="text-lg font-medium text-temple mb-4">Szybkie statystyki</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-temple/5 rounded-lg">
              <div className="text-2xl font-bold text-temple">-</div>
              <div className="text-sm text-wood-light">Rezerwacje</div>
            </div>
            <div className="text-center p-4 bg-golden/5 rounded-lg">
              <div className="text-2xl font-bold text-temple">-</div>
              <div className="text-sm text-wood-light">Newsletter</div>
            </div>
            <div className="text-center p-4 bg-sage/5 rounded-lg">
              <div className="text-2xl font-bold text-temple">-</div>
              <div className="text-sm text-wood-light">Odwiedziny</div>
            </div>
            <div className="text-center p-4 bg-lotus/5 rounded-lg">
              <div className="text-2xl font-bold text-temple">-</div>
              <div className="text-sm text-wood-light">Konwersje</div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
