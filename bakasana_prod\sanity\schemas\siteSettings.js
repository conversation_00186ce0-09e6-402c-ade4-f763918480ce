// sanity/schemas/siteSettings.js
export default {
  name: 'siteSettings',
  title: 'Ustawienia strony',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Tytuł strony',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'description',
      title: 'Opis strony',
      type: 'text',
      validation: Rule => Rule.required()
    },
    {
      name: 'keywords',
      title: 'Słowa kluczowe',
      type: 'array',
      of: [{type: 'string'}]
    },
    {
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true
      },
      fields: [
        {
          name: 'alt',
          title: 'Tekst alternatywny',
          type: 'string'
        }
      ]
    },
    {
      name: 'socialMedia',
      title: 'Media społecznościowe',
      type: 'object',
      fields: [
        {
          name: 'facebook',
          title: 'Facebook URL',
          type: 'url'
        },
        {
          name: 'instagram',
          title: 'Instagram URL',
          type: 'url'
        },
        {
          name: 'youtube',
          title: 'YouTube URL',
          type: 'url'
        },
        {
          name: 'whatsapp',
          title: 'WhatsApp (numer telefonu)',
          type: 'string',
          description: 'Format: 48606101523'
        }
      ]
    },
    {
      name: 'contact',
      title: 'Kontakt',
      type: 'object',
      fields: [
        {
          name: 'email',
          title: 'Email',
          type: 'string',
          validation: Rule => Rule.email()
        },
        {
          name: 'phone',
          title: 'Telefon',
          type: 'string'
        },
        {
          name: 'address',
          title: 'Adres',
          type: 'text'
        }
      ]
    },
    {
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        {
          name: 'metaTitle',
          title: 'Meta tytuł',
          type: 'string',
          validation: Rule => Rule.max(60)
        },
        {
          name: 'metaDescription',
          title: 'Meta opis',
          type: 'text',
          validation: Rule => Rule.max(160)
        },
        {
          name: 'ogImage',
          title: 'Obraz Open Graph',
          type: 'image',
          options: {
            hotspot: true
          },
          fields: [
            {
              name: 'alt',
              title: 'Tekst alternatywny',
              type: 'string'
            }
          ]
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title',
      media: 'logo'
    }
  }
}
