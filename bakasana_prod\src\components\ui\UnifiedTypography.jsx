'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedTypography - Ujednolicony system typografii BAKASANA
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

// Heading Components
export function HeroTitle({ children, className = '', ...props }) {
  return (
    <h1
      className={cn(
        "font-primary font-light text-charcoal leading-none",
        "tracking-ultra mb-6 text-center",
        "text-hero",
        className
      )}
      style={{
        textShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}
      {...props}
    >
      {children}
    </h1>
  );
}

export function SectionTitle({ children, level = 2, className = '', ...props }) {
  const Tag = `h${level}`;

  return (
    <Tag
      className={cn(
        "font-primary font-light text-charcoal leading-tight",
        "text-display-xl tracking-wider",
        "mb-12 text-center",
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function CardTitle({ children, level = 3, className = '', ...props }) {
  const Tag = `h${level}`;

  return (
    <Tag
      className={cn(
        "font-primary font-normal text-charcoal leading-snug",
        "text-heading-lg tracking-wide",
        "mb-4",
        className
      )}
      {...props}
    >
      {children}
    </Tag>
  );
}

export function SubTitle({ children, className = '', ...props }) {
  return (
    <h4 
      className={cn(
        "font-helvetica font-light text-charcoal",
        "tracking-[0.15em] leading-relaxed",
        "mb-6 text-center opacity-70",
        className
      )}
      style={{
        fontSize: '16px',
        marginTop: '40px'
      }}
      {...props}
    >
      {children}
    </h4>
  );
}

// Body Text Components
export function BodyText({ children, size = 'md', className = '', ...props }) {
  const sizeClasses = {
    sm: "text-sm leading-[1.6]",
    md: "text-base leading-[1.75]",
    lg: "text-lg leading-[1.8]"
  };

  return (
    <p
      className={cn(
        "font-inter font-light text-charcoal-light",
        sizeClasses[size],
        "mb-6",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function LeadText({ children, className = '', ...props }) {
  return (
    <p 
      className={cn(
        "font-inter font-light text-charcoal",
        "text-xl leading-relaxed",
        "mb-8 max-w-3xl mx-auto text-center",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

export function SmallText({ children, className = '', ...props }) {
  return (
    <p 
      className={cn(
        "font-inter font-light text-sage",
        "text-sm leading-relaxed",
        "mb-4",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
}

// Special Text Components
export function Quote({ children, author, className = '', ...props }) {
  return (
    <blockquote 
      className={cn(
        "font-cormorant italic text-enterprise-brown",
        "text-2xl leading-relaxed text-center",
        "mb-8 max-w-2xl mx-auto",
        "relative",
        className
      )}
      {...props}
    >
      <span className="text-4xl opacity-30 absolute -top-4 -left-4">"</span>
      {children}
      <span className="text-4xl opacity-30 absolute -bottom-8 -right-4">"</span>
      {author && (
        <cite className="block font-inter font-light text-sage text-sm mt-4 not-italic">
          — {author}
        </cite>
      )}
    </blockquote>
  );
}

export function Badge({ children, variant = 'default', className = '', ...props }) {
  const variants = {
    default: "bg-enterprise-brown text-sanctuary",
    outline: "border border-enterprise-brown text-enterprise-brown bg-transparent",
    warm: "bg-terra text-sanctuary",
    minimal: "bg-whisper text-charcoal"
  };
  
  return (
    <span 
      className={cn(
        "inline-block px-3 py-1 text-xs font-inter font-medium",
        "uppercase tracking-[2px] transition-all duration-300",
        variants[variant],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}

// Navigation Text - Enhanced with elegant hover effects
export function NavLink({ children, active = false, className = '', ...props }) {
  return (
    <span
      className={cn(
        "relative font-inter font-light uppercase tracking-[2px] group cursor-pointer",
        "transition-all duration-300 ease-out",
        active
          ? "text-enterprise-brown opacity-100 text-sm"
          : "text-charcoal-light opacity-60 hover:opacity-100 hover:text-enterprise-brown text-sm",
        className
      )}
      {...props}
    >
      {children}
      {/* Elegant underline that flows from center */}
      <span
        className={cn(
          "absolute -bottom-1 left-1/2 h-[1px] bg-gradient-to-r from-transparent via-enterprise-brown to-transparent",
          "transition-all duration-300 ease-out transform -translate-x-1/2",
          active
            ? "w-full opacity-100"
            : "w-0 opacity-0 group-hover:w-full group-hover:opacity-100"
        )}
      />
    </span>
  );
}

// Form Labels
export function FormLabel({ children, required = false, className = '', ...props }) {
  return (
    <label 
      className={cn(
        "block text-sm font-inter font-light text-charcoal",
        "mb-2 tracking-wide",
        className
      )}
      {...props}
    >
      {children}
      {required && (
        <span className="text-terra ml-1" aria-label="wymagane">*</span>
      )}
    </label>
  );
}

// Stats/Numbers
export function StatNumber({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "font-cormorant font-extralight text-enterprise-brown",
        "text-5xl leading-none mb-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export function StatLabel({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "font-inter font-medium text-sage",
        "text-xs uppercase tracking-[2px]",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Utility Components
export function Divider({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center justify-center my-12",
        className
      )}
      {...props}
    >
      <div className="flex-1 h-px bg-stone-light max-w-20"></div>
      <div className="w-1.5 h-1.5 bg-enterprise-brown transform rotate-45 mx-6"></div>
      <div className="flex-1 h-px bg-stone-light max-w-20"></div>
    </div>
  );
}

export function OrganicAccent({ className = '', ...props }) {
  return (
    <div 
      className={cn(
        "w-12 h-0.5 bg-gradient-to-r from-transparent via-enterprise-brown to-transparent",
        "mx-auto mb-8 opacity-60",
        className
      )}
      {...props}
    />
  );
}