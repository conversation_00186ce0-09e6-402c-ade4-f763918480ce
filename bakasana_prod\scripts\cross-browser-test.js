#!/usr/bin/env node

/**
 * 🌐 CROSS-BROWSER TESTING SCRIPT
 * Tests Bakasana website across different browsers and devices
 */

const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

class CrossBrowserTester {
  constructor() {
    this.testResults = {
      timestamp: new Date().toISOString(),
      browsers: {},
      summary: {
        totalTests: 0,
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
    
    this.testPages = [
      { name: 'Homepage', url: 'http://localhost:3002' },
      { name: 'About', url: 'http://localhost:3002/o-mnie' },
      { name: 'Contact', url: 'http://localhost:3002/kontakt' },
      { name: 'Retreats', url: 'http://localhost:3002/retreaty' }
    ];
  }

  async runTests() {
    console.log('🌐 Starting Cross-Browser Compatibility Tests...\n');
    
    // Test different browser configurations
    const browserConfigs = [
      {
        name: 'Chrome Desktop',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1920, height: 1080 }
      },
      {
        name: 'Firefox Desktop',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        viewport: { width: 1920, height: 1080 }
      },
      {
        name: 'Safari Desktop',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
        viewport: { width: 1440, height: 900 }
      },
      {
        name: 'Edge Desktop',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        viewport: { width: 1920, height: 1080 }
      },
      {
        name: 'Mobile Chrome',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/120.0.0.0 Mobile/15E148 Safari/604.1',
        viewport: { width: 375, height: 812 }
      }
    ];

    for (const config of browserConfigs) {
      await this.testBrowser(config);
    }

    await this.generateReport();
    console.log('\n✅ Cross-browser testing completed!');
    console.log(`📊 Results: ${this.testResults.summary.passed}/${this.testResults.summary.totalTests} tests passed`);
  }

  async testBrowser(config) {
    console.log(`🔍 Testing ${config.name}...`);
    
    const browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
      const page = await browser.newPage();
      await page.setUserAgent(config.userAgent);
      await page.setViewport(config.viewport);

      this.testResults.browsers[config.name] = {
        config,
        pages: {},
        summary: { passed: 0, failed: 0, warnings: 0 }
      };

      for (const testPage of this.testPages) {
        const result = await this.testPage(page, testPage, config.name);
        this.testResults.browsers[config.name].pages[testPage.name] = result;
        
        if (result.status === 'passed') this.testResults.browsers[config.name].summary.passed++;
        else if (result.status === 'failed') this.testResults.browsers[config.name].summary.failed++;
        else this.testResults.browsers[config.name].summary.warnings++;
      }

    } catch (error) {
      console.error(`❌ Error testing ${config.name}:`, error.message);
    } finally {
      await browser.close();
    }
  }

  async testPage(page, testPage, browserName) {
    console.log(`  📄 Testing ${testPage.name}...`);
    
    const result = {
      url: testPage.url,
      status: 'passed',
      tests: {},
      errors: [],
      warnings: [],
      performance: {}
    };

    try {
      // Navigate to page
      const response = await page.goto(testPage.url, { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });

      if (!response.ok()) {
        result.status = 'failed';
        result.errors.push(`HTTP ${response.status()}: ${response.statusText()}`);
        return result;
      }

      // Test CSS Grid support
      result.tests.cssGrid = await this.testCSSGrid(page);
      
      // Test Flexbox support
      result.tests.flexbox = await this.testFlexbox(page);
      
      // Test responsive design
      result.tests.responsive = await this.testResponsive(page);
      
      // Test accessibility
      result.tests.accessibility = await this.testAccessibility(page);
      
      // Test performance
      result.performance = await this.testPerformance(page);

      // Check for JavaScript errors
      const jsErrors = await this.getJavaScriptErrors(page);
      if (jsErrors.length > 0) {
        result.warnings.push(`JavaScript errors: ${jsErrors.length}`);
      }

      this.testResults.summary.totalTests++;
      if (result.status === 'passed') this.testResults.summary.passed++;
      else if (result.status === 'failed') this.testResults.summary.failed++;
      else this.testResults.summary.warnings++;

    } catch (error) {
      result.status = 'failed';
      result.errors.push(error.message);
      this.testResults.summary.totalTests++;
      this.testResults.summary.failed++;
    }

    return result;
  }

  async testCSSGrid(page) {
    return await page.evaluate(() => {
      const gridElements = document.querySelectorAll('.destinations-grid, .magazine-grid, [style*="display: grid"], [class*="grid"]');
      const results = {
        supported: CSS.supports('display', 'grid'),
        elementsFound: gridElements.length,
        workingElements: 0
      };

      gridElements.forEach(el => {
        const styles = window.getComputedStyle(el);
        if (styles.display === 'grid') {
          results.workingElements++;
        }
      });

      return results;
    });
  }

  async testFlexbox(page) {
    return await page.evaluate(() => {
      const flexElements = document.querySelectorAll('[style*="display: flex"], [class*="flex"]');
      const results = {
        supported: CSS.supports('display', 'flex'),
        elementsFound: flexElements.length,
        workingElements: 0
      };

      flexElements.forEach(el => {
        const styles = window.getComputedStyle(el);
        if (styles.display === 'flex') {
          results.workingElements++;
        }
      });

      return results;
    });
  }

  async testResponsive(page) {
    const viewports = [
      { width: 375, height: 812, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1920, height: 1080, name: 'Desktop' }
    ];

    const results = {};

    for (const viewport of viewports) {
      await page.setViewport(viewport);
      await page.waitForTimeout(500);

      results[viewport.name] = await page.evaluate(() => {
        return {
          hasHorizontalScroll: document.body.scrollWidth > window.innerWidth,
          hasOverflowElements: document.querySelectorAll('[style*="overflow-x: scroll"]').length > 0
        };
      });
    }

    return results;
  }

  async testAccessibility(page) {
    return await page.evaluate(() => {
      const results = {
        missingAltImages: document.querySelectorAll('img:not([alt])').length,
        emptyHeadings: document.querySelectorAll('h1:empty, h2:empty, h3:empty, h4:empty, h5:empty, h6:empty').length,
        missingLabels: document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])').length
      };

      return results;
    });
  }

  async testPerformance(page) {
    const metrics = await page.metrics();
    return {
      JSHeapUsedSize: Math.round(metrics.JSHeapUsedSize / 1024 / 1024 * 100) / 100, // MB
      JSHeapTotalSize: Math.round(metrics.JSHeapTotalSize / 1024 / 1024 * 100) / 100, // MB
      ScriptDuration: Math.round(metrics.ScriptDuration * 1000) / 1000, // seconds
      TaskDuration: Math.round(metrics.TaskDuration * 1000) / 1000 // seconds
    };
  }

  async getJavaScriptErrors(page) {
    return await page.evaluate(() => {
      return window.__jsErrors || [];
    });
  }

  async generateReport() {
    const reportPath = path.join(__dirname, '..', 'reports', 'cross-browser-test.json');
    
    // Ensure reports directory exists
    await fs.mkdir(path.dirname(reportPath), { recursive: true });
    
    // Save detailed JSON report
    await fs.writeFile(reportPath, JSON.stringify(this.testResults, null, 2));
    
    // Generate HTML report
    const htmlReport = this.generateHTMLReport();
    const htmlPath = path.join(__dirname, '..', 'reports', 'cross-browser-test.html');
    await fs.writeFile(htmlPath, htmlReport);
    
    console.log(`📊 Reports saved:`);
    console.log(`   JSON: ${reportPath}`);
    console.log(`   HTML: ${htmlPath}`);
  }

  generateHTMLReport() {
    return `
<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cross-Browser Test Report - Bakasana</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 40px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .browser { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }
        .browser-header { background: #333; color: white; padding: 15px; }
        .browser-content { padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .passed { background: #d4edda; color: #155724; }
        .failed { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🌐 Cross-Browser Test Report</h1>
    <p>Generated: ${this.testResults.timestamp}</p>
    
    <div class="summary">
        <h2>📊 Summary</h2>
        <p><strong>Total Tests:</strong> ${this.testResults.summary.totalTests}</p>
        <p><strong>Passed:</strong> ${this.testResults.summary.passed}</p>
        <p><strong>Failed:</strong> ${this.testResults.summary.failed}</p>
        <p><strong>Warnings:</strong> ${this.testResults.summary.warnings}</p>
    </div>

    ${Object.entries(this.testResults.browsers).map(([browserName, browserData]) => `
        <div class="browser">
            <div class="browser-header">
                <h3>${browserName}</h3>
                <p>Passed: ${browserData.summary.passed} | Failed: ${browserData.summary.failed} | Warnings: ${browserData.summary.warnings}</p>
            </div>
            <div class="browser-content">
                ${Object.entries(browserData.pages).map(([pageName, pageData]) => `
                    <div class="test-result ${pageData.status}">
                        <h4>${pageName}</h4>
                        <p><strong>Status:</strong> ${pageData.status}</p>
                        <p><strong>CSS Grid:</strong> ${pageData.tests.cssGrid?.supported ? '✅' : '❌'} (${pageData.tests.cssGrid?.workingElements}/${pageData.tests.cssGrid?.elementsFound} elements)</p>
                        <p><strong>Flexbox:</strong> ${pageData.tests.flexbox?.supported ? '✅' : '❌'} (${pageData.tests.flexbox?.workingElements}/${pageData.tests.flexbox?.elementsFound} elements)</p>
                        ${pageData.errors.length > 0 ? `<p><strong>Errors:</strong> ${pageData.errors.join(', ')}</p>` : ''}
                        ${pageData.warnings.length > 0 ? `<p><strong>Warnings:</strong> ${pageData.warnings.join(', ')}</p>` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('')}
</body>
</html>`;
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new CrossBrowserTester();
  tester.runTests().catch(console.error);
}

module.exports = CrossBrowserTester;
