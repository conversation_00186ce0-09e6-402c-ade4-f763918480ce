# 🎨 BAKASANA DESIGN SYSTEM

## Overview

The Bakasana Design System embodies **"Old Money + Warm Minimalism"** aesthetic - sophisticated, timeless, and organically warm. This system provides a unified foundation for all visual elements across the Bakasana website.

## 🎯 Design Philosophy

- **Old Money Elegance**: Timeless sophistication without ostentation
- **Warm Minimalism**: Clean design with organic warmth
- **Accessibility First**: WCAG AA compliant throughout
- **Consistency**: Single source of truth for all design decisions
- **Scalability**: Flexible system that grows with the brand

---

## 🎨 Color System

### Primary Colors

```css
--sanctuary: #FDFCF8;           /* Main background - warm cream */
--charcoal: #2A2724;            /* Main text - warm dark (12.8:1 contrast) */
--enterprise-brown: #8B7355;    /* Primary accent - sophisticated */
--temple-gold: #B8935C;         /* Secondary accent - warm gold */
```

### Extended Palette

```css
/* Warm Backgrounds */
--parchment: #FCF6EE;           /* Hero background - subtle warmth */
--linen: #F9F6F1;               /* Subtle backgrounds - organic linen */
--whisper: #F5F2ED;             /* Ultra-subtle - barely there */
--silk: #F0EDE8;                /* Cards - soft silk texture */

/* Text Hierarchy (WCAG AA Compliant) */
--charcoal-light: #4A453F;      /* Lighter text - muted warmth (7.2:1) */
--ash: #5A524D;                 /* Secondary text (5.8:1) */
--sage: #6B6560;                /* Subtle text (4.6:1) */
--stone: #8B8680;               /* Light text (3.2:1) */
--stone-light: #C4BFB8;         /* Very light - barely visible */

/* Accent Colors */
--terra: #B8935C;               /* Alias for temple-gold - unified */
--sand: #D4AF7A;                /* Golden highlights - desert sand */
--amber: #E6C18A;               /* Light accents - warm amber */
```

### Usage Guidelines

- **sanctuary**: Main page backgrounds, form backgrounds
- **charcoal**: Primary text, headings, important content
- **enterprise-brown**: Primary buttons, active states, brand elements
- **temple-gold**: Hover states, secondary actions, highlights
- **sage**: Placeholder text, subtle labels, secondary information

---

## 📝 Typography System

### Font Families

```css
--font-primary: 'Cormorant Garamond', 'Didot', 'Bodoni MT', 'Playfair Display', serif;
--font-secondary: 'Inter', 'Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', sans-serif;
```

### Typography Scale

```css
--text-hero: clamp(100px, 15vw, 200px);    /* Hero titles */
--text-display-xl: 3rem;                   /* 48px - Major headings */
--text-display: 2.5rem;                    /* 40px - Section titles */
--text-heading-lg: 2rem;                   /* 32px - Card titles */
--text-heading: 1.5rem;                    /* 24px - Subheadings */
--text-subtitle: 1.3125rem;                /* 21px - Subtitles */
--text-body-lg: 1.0625rem;                 /* 17px - Large body text */
--text-body: 1rem;                         /* 16px - Standard body text */
--text-caption: 0.875rem;                  /* 14px - Captions */
--text-small: 0.8125rem;                   /* 13px - Small text */
--text-micro: 0.6875rem;                   /* 11px - Micro text */
```

### Letter Spacing

```css
--tracking-tightest: -0.05em;
--tracking-tight: -0.025em;
--tracking-normal: 0;
--tracking-wide: 0.025em;
--tracking-wider: 0.05em;
--tracking-widest: 0.1em;
--tracking-ultra: 0.2em;                   /* For hero titles and logos */
```

### Line Heights

```css
--leading-none: 1;                         /* Hero titles */
--leading-tight: 1.1;                      /* Large headings */
--leading-snug: 1.2;                       /* Card titles */
--leading-normal: 1.3;                     /* Headings */
--leading-relaxed: 1.5;                    /* Body text */
--leading-loose: 1.8;                      /* Long-form content */
```

### Usage Guidelines

- **font-primary**: All headings, titles, brand elements
- **font-secondary**: Body text, UI elements, navigation
- **Hero text**: Use `text-hero` with `font-primary` and `tracking-ultra`
- **Body text**: Use `text-body` with `font-secondary` and `leading-relaxed`

---

## 🔘 Interactive Elements

### Button System

#### Primary Button
```jsx
<button className="bg-enterprise-brown text-sanctuary border border-enterprise-brown hover:bg-temple-gold hover:border-temple-gold hover:shadow-hover transition-all duration-normal">
  Primary Action
</button>
```

#### Secondary Button
```jsx
<button className="bg-transparent text-enterprise-brown border border-enterprise-brown hover:bg-enterprise-brown hover:text-sanctuary transition-all duration-normal">
  Secondary Action
</button>
```

#### Ghost Button
```jsx
<button className="bg-transparent text-charcoal hover:bg-whisper hover:text-enterprise-brown transition-all duration-normal">
  Ghost Action
</button>
```

### Form Elements

#### Input Field
```jsx
<input className="bg-sanctuary border border-stone-light text-charcoal focus:border-enterprise-brown focus:shadow-focus focus:outline-none transition-all duration-normal" />
```

#### Textarea
```jsx
<textarea className="bg-sanctuary border border-stone-light text-charcoal focus:border-enterprise-brown focus:shadow-focus focus:outline-none transition-all duration-normal resize-none" />
```

### Card System

#### Default Card
```jsx
<div className="bg-sanctuary border border-stone-light hover:shadow-elegant hover:border-enterprise-brown hover:-translate-y-1 transition-all duration-normal">
  Card Content
</div>
```

#### Elevated Card
```jsx
<div className="bg-sanctuary shadow-elegant border border-enterprise-brown hover:shadow-premium hover:border-temple-gold hover:-translate-y-2 transition-all duration-normal">
  Elevated Content
</div>
```

---

## 🎯 Icon System

### Icon Sizes

```css
xs: w-3 h-3     /* 12px - Extra small icons */
sm: w-4 h-4     /* 16px - Small icons */
md: w-5 h-5     /* 20px - Default icons */
lg: w-6 h-6     /* 24px - Large icons */
xl: w-8 h-8     /* 32px - Extra large icons */
2xl: w-10 h-10  /* 40px - Hero icons */
3xl: w-12 h-12  /* 48px - Feature icons */
```

### Icon Colors

```css
primary: text-enterprise-brown     /* Primary actions, active states */
secondary: text-temple-gold        /* Secondary actions, highlights */
muted: text-stone                  /* Inactive states, subtle elements */
subtle: text-sage                  /* Placeholder icons, very subtle */
contrast: text-charcoal            /* High contrast, important icons */
light: text-stone-light            /* Very light, background icons */
```

### Usage Examples

```jsx
import Icon from '@/components/ui/Icon';

// Navigation icon
<Icon name="menu" size="md" color="primary" />

// Button icon
<Icon name="arrow-right" size="sm" color="inherit" />

// Feature icon
<Icon name="heart" size="2xl" color="primary" />
```

---

## 🌊 Shadow System

```css
--shadow-subtle: 0 2px 8px rgba(26, 24, 22, 0.06);
--shadow-elegant: 0 4px 16px rgba(139, 115, 85, 0.08);
--shadow-elevated: 0 8px 25px rgba(26, 24, 22, 0.08);
--shadow-premium: 0 12px 40px rgba(139, 115, 85, 0.12);
--shadow-hero: 0 20px 60px rgba(26, 24, 22, 0.08);

/* Interactive Shadows */
--shadow-hover: 0 8px 25px rgba(139, 115, 85, 0.15);
--shadow-focus: 0 0 0 3px rgba(139, 115, 85, 0.1);
```

### Usage Guidelines

- **shadow-subtle**: Default cards, form elements
- **shadow-elegant**: Hover states, elevated content
- **shadow-premium**: Important cards, modals
- **shadow-focus**: Focus states for accessibility

---

## ⏱️ Animation System

### Duration Scale

```css
--duration-instant: 0.15s;         /* Micro-interactions */
--duration-fast: 0.2s;             /* Quick feedback */
--duration-normal: 0.3s;           /* Standard transitions */
--duration-slow: 0.5s;             /* Complex animations */
--duration-entrance: 0.8s;         /* Page/component entrance */
```

### Easing Functions

```css
--ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
--ease-spring: cubic-bezier(0.25, 0.46, 0.45, 0.94);
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
--ease-gentle: cubic-bezier(0.25, 0.1, 0.25, 1);
```

### Usage Guidelines

- **duration-normal**: Default for all transitions
- **duration-fast**: Hover states, button interactions
- **duration-slow**: Complex state changes, page transitions
- **ease-smooth**: Default easing for most animations

---

## 📏 Spacing System

### Base Scale (8px base)

```css
--space-xs: 0.5rem;                /* 8px */
--space-sm: 1rem;                  /* 16px */
--space-md: 1.5rem;                /* 24px */
--space-lg: 2rem;                  /* 32px */
--space-xl: 3rem;                  /* 48px */
--space-2xl: 4rem;                 /* 64px */
--space-3xl: 5rem;                 /* 80px */
--space-4xl: 7.5rem;               /* 120px */
```

### Specialized Spacing

```css
--space-section: 7.5rem;           /* 120px - Section spacing */
--space-container: 8%;             /* Luxury side margins */
--space-breathe: 11.25rem;         /* 180px - Ultra-premium breathing */
--space-card: 3rem;                /* 48px - Card internal padding */
--space-hero: 1.5rem;              /* 24px - Hero section padding */
--space-nav: 5rem;                 /* 80px - Navigation height */
```

---

## 🎭 Component Usage Examples

### Hero Section
```jsx
<section className="bg-parchment py-breathe px-container">
  <h1 className="font-primary text-hero font-light tracking-ultra text-charcoal leading-none text-center mb-xl">
    BAKASANA
  </h1>
  <p className="font-secondary text-body-lg text-sage leading-relaxed text-center max-w-2xl mx-auto">
    Discover inner peace through transformative yoga retreats
  </p>
</section>
```

### Card Component
```jsx
<div className="bg-sanctuary border border-stone-light p-card hover:shadow-elegant hover:border-enterprise-brown hover:-translate-y-1 transition-all duration-normal">
  <Icon name="heart" size="lg" color="primary" className="mb-md" />
  <h3 className="font-primary text-heading-lg font-normal text-charcoal tracking-wide mb-sm">
    Mindful Practice
  </h3>
  <p className="font-secondary text-body text-sage leading-relaxed">
    Connect with your inner self through guided meditation and breathwork.
  </p>
</div>
```

### Navigation Link
```jsx
<Link 
  href="/retreats" 
  className="font-secondary text-small font-normal tracking-widest text-charcoal hover:text-enterprise-brown transition-all duration-normal uppercase"
>
  Retreats
</Link>
```

---

## 🔧 Implementation Guidelines

### CSS Custom Properties
All design tokens are defined in `src/styles/design-tokens.css` and should be used via CSS custom properties or Tailwind classes.

### Tailwind Configuration
The `tailwind.config.js` file extends Tailwind with our design tokens, making them available as utility classes.

### Component Structure
- Use unified components from `src/components/ui/`
- Follow the established patterns for consistency
- Always use design tokens instead of hardcoded values

### Accessibility
- All color combinations meet WCAG AA standards
- Focus states are clearly defined
- Interactive elements have proper hover/focus states
- Icons include proper aria-labels when needed

---

## 📱 Responsive Design

### Breakpoints
```css
xs: 480px    /* Small mobile to mobile */
sm: 768px    /* Mobile to tablet */
md: 1024px   /* Tablet to desktop */
lg: 1440px   /* Desktop to large desktop */
xl: 1920px   /* Large desktop and up */
```

### Responsive Typography
Use `clamp()` functions for fluid typography that scales smoothly across devices.

### Mobile-First Approach
All components are designed mobile-first with progressive enhancement for larger screens.

---

## 🎨 Brand Guidelines

### Logo Usage
- Always use the gradient logo treatment for the BAKASANA wordmark
- Maintain proper spacing around the logo
- Use `tracking-ultra` for consistent letter spacing

### Voice & Tone
- Sophisticated yet approachable
- Warm and inviting
- Mindful and intentional
- Never rushed or aggressive

### Photography Style
- Warm, natural lighting
- Organic compositions
- Muted, earthy tones
- Focus on human connection and nature

---

## 🔄 Maintenance

### Adding New Components
1. Follow existing patterns in `src/components/ui/`
2. Use design tokens from `design-tokens.css`
3. Ensure WCAG AA compliance
4. Test across all breakpoints
5. Update this documentation

### Updating Design Tokens
1. Modify values in `src/styles/design-tokens.css`
2. Update Tailwind config if needed
3. Test across all components
4. Update documentation

### Version Control
- All changes should be documented
- Breaking changes require major version bump
- Maintain backward compatibility when possible

---

## 🧩 Component Library

### UnifiedButton
```jsx
import { UnifiedButton } from '@/components/ui/UnifiedButton';

<UnifiedButton variant="primary" size="lg">
  Book Retreat
</UnifiedButton>
```

### UnifiedInput
```jsx
import { UnifiedInput } from '@/components/ui/UnifiedInput';

<UnifiedInput
  variant="default"
  size="md"
  placeholder="Enter your email"
  error={false}
/>
```

### UnifiedCard
```jsx
import { UnifiedCard } from '@/components/ui/UnifiedCard';

<UnifiedCard variant="elevated" padding="lg">
  <h3>Card Title</h3>
  <p>Card content goes here...</p>
</UnifiedCard>
```

### UnifiedTypography
```jsx
import { HeroTitle, SectionTitle, CardTitle } from '@/components/ui/UnifiedTypography';

<HeroTitle>BAKASANA</HeroTitle>
<SectionTitle>Our Retreats</SectionTitle>
<CardTitle>Mindful Practice</CardTitle>
```

### Icon System
```jsx
import Icon, { NavigationIcon, ButtonIcon, StatusIcon } from '@/components/ui/Icon';

<Icon name="heart" size="lg" color="primary" />
<NavigationIcon name="menu" active={true} />
<ButtonIcon name="arrow-right" />
<StatusIcon name="check-circle" status="success" />
```

---

## 🎯 Quick Reference

### Most Common Patterns

#### Button Styles
```css
/* Primary CTA */
.btn-primary {
  @apply bg-enterprise-brown text-sanctuary border border-enterprise-brown hover:bg-temple-gold hover:border-temple-gold hover:shadow-hover transition-all duration-normal;
}

/* Secondary Action */
.btn-secondary {
  @apply bg-transparent text-enterprise-brown border border-enterprise-brown hover:bg-enterprise-brown hover:text-sanctuary transition-all duration-normal;
}
```

#### Text Styles
```css
/* Hero Title */
.hero-title {
  @apply font-primary text-hero font-light tracking-ultra text-charcoal leading-none;
}

/* Body Text */
.body-text {
  @apply font-secondary text-body text-sage leading-relaxed;
}
```

#### Card Styles
```css
/* Default Card */
.card-default {
  @apply bg-sanctuary border border-stone-light hover:shadow-elegant hover:border-enterprise-brown hover:-translate-y-1 transition-all duration-normal;
}
```

---

## 📚 Resources

- [Design Tokens File](./src/styles/design-tokens.css)
- [Tailwind Config](./tailwind.config.js)
- [Component Library](./src/components/ui/)
- [Icon System](./src/components/ui/Icon.jsx)
- [Typography Components](./src/components/ui/UnifiedTypography.jsx)
- [Button Components](./src/components/ui/UnifiedButton.jsx)
- [Form Components](./src/components/ui/UnifiedInput.jsx)
- [Card Components](./src/components/ui/UnifiedCard.jsx)

---

*Last updated: 2025-07-21*
*Version: 1.0.0*
