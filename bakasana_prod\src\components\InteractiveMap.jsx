'use client';

import { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ScrollReveal } from './ScrollReveal';

// Temporarily disable map due to import issues
// TODO: Fix react-map-gl import in future update
const Map = null;
const Marker = null;
const Popup = null;

// Bali locations data
const baliLocations = [
  {
    id: 'ubud',
    name: 'Ubud',
    coordinates: [115.2624, -8.5069],
    type: 'spiritual',
    description: 'Duchowe serce Bali z tarasami ryżowymi i świątyniami',
    image: '/images/gallery/ubud-rice-terraces.webp',
    highlights: [
      'Tarasy ryżowe Tegallalang',
      'Świątynia Tirta Empul',
      'Monkey Forest Sanctuary',
      'Tradycyjne warsztaty rękodzieła'
    ],
    yogaSpots: [
      'The Yoga Barn',
      'Radiantly Alive',
      'Intuitive Flow'
    ]
  },
  {
    id: 'uluwatu',
    name: '<PERSON><PERSON>watu',
    coordinates: [115.0847, -8.8290],
    type: 'beach',
    description: '<PERSON>pektakular<PERSON> klify z widokiem na ocean i świątynia na skale',
    image: '/images/gallery/uluwatu-temple.webp',
    highlights: [
      'Świątynia Pura Luhur Uluwatu',
      'Plaża Padang Padang',
      'Taniec Kecak o zachodzie słońca',
      'Surfing spots'
    ],
    yogaSpots: [
      'Yoga na klifach',
      'Sunset yoga sessions',
      'Beach meditation'
    ]
  },
  {
    id: 'canggu',
    name: 'Canggu',
    coordinates: [115.1398, -8.6482],
    type: 'beach',
    description: 'Surferski raj z czarnymi plażami i relaksowaną atmosferą',
    image: '/images/gallery/canggu-beach.webp',
    highlights: [
      'Plaża Echo Beach',
      'Tanah Lot Temple',
      'Surfing i beach clubs',
      'Lokalne warung'
    ],
    yogaSpots: [
      'Desa Seni',
      'The Practice Bali',
      'Samadi Bali'
    ]
  },
  {
    id: 'jatiluwih',
    name: 'Jatiluwih',
    coordinates: [115.1333, -8.3667],
    type: 'nature',
    description: 'Magiczne tarasy ryżowe wpisane na listę UNESCO',
    image: '/images/gallery/jatiluwih-terraces.webp',
    highlights: [
      'Tarasy ryżowe UNESCO',
      'Trekking przez pola',
      'Tradycyjne wioski',
      'Widoki na wulkan Batukaru'
    ],
    yogaSpots: [
      'Yoga w tarasach ryżowych',
      'Meditation walks',
      'Sunrise practice'
    ]
  },
  {
    id: 'sekumpul',
    name: 'Sekumpul Waterfall',
    coordinates: [115.0833, -8.2167],
    type: 'nature',
    description: 'Najpiękniejszy wodospad Bali ukryty w dżungli',
    image: '/images/gallery/sekumpul-waterfall.webp',
    highlights: [
      'Wodospad Sekumpul',
      'Trekking przez dżunglę',
      'Naturalne baseny',
      'Fotografia przyrody'
    ],
    yogaSpots: [
      'Waterfall meditation',
      'Nature yoga',
      'Sound healing'
    ]
  },
  {
    id: 'gili-air',
    name: 'Gili Air',
    coordinates: [116.0833, -8.3500],
    type: 'island',
    description: 'Rajska wyspa bez samochodów, idealna na relaks',
    image: '/images/gallery/gili-air.webp',
    highlights: [
      'Krystalicznie czysta woda',
      'Snorkeling z żółwiami',
      'Brak samochodów',
      'Lokalne seafood'
    ],
    yogaSpots: [
      'Beach yoga',
      'Sunset meditation',
      'Ocean breathing'
    ]
  }
];

const getMarkerColor = (type) => {
  switch (type) {
    case 'spiritual': return '#8B7355'; // temple
    case 'beach': return '#F59E0B'; // golden
    case 'nature': return '#10B981'; // emerald
    case 'island': return '#3B82F6'; // blue
    default: return '#8B7355';
  }
};

export default function InteractiveMap() {
  const [viewState, setViewState] = useState({
    longitude: 115.2191,
    latitude: -8.6405,
    zoom: 9
  });
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [hoveredLocation, setHoveredLocation] = useState(null);
  const [isMapReady, setIsMapReady] = useState(false);

  // Initialize map after component mounts
  useEffect(() => {
    if (typeof window !== 'undefined' && Map && Marker) {
      setIsMapReady(true);
    }
  }, []);

  const onMarkerClick = useCallback((location) => {
    setSelectedLocation(location);
    setViewState(prev => ({
      ...prev,
      longitude: location.coordinates[0],
      latitude: location.coordinates[1],
      zoom: 11
    }));
  }, []);

  return (
    <div className="space-y-8">
      <ScrollReveal className="text-center">
        <h2 className="text-3xl md:text-4xl font-serif text-enterprise-brown mb-4">
          Mapa Miejsc na Bali
        </h2>
        <p className="text-wood-light text-lg max-w-2xl mx-auto">
          Odkryj magiczne miejsca, które odwiedzimy podczas retreatu. 
          Kliknij na pin, aby poznać szczegóły.
        </p>
      </ScrollReveal>

      {/* Legend */}
      <div className="flex flex-wrap justify-center gap-4 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-enterprise-brown rectangular"></div>
          <span>Miejsca duchowe</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-temple-gold rectangular"></div>
          <span>Plaże</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-emerald-500 rectangular"></div>
          <span>Przyroda</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-blue-500 rectangular"></div>
          <span>Wyspy</span>
        </div>
      </div>

      {/* Map Container */}
      <ScrollReveal className="relative h-[600px] rectangular overflow-hidden shadow-soft">
        {isMapReady && Map && Marker ? (
          <Map
            {...viewState}
            onMove={evt => setViewState(evt.viewState)}
            mapStyle="mapbox://styles/mapbox/satellite-streets-v12"
            mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN || 'pk.eyJ1IjoiZXhhbXBsZSIsImEiOiJjazl2a2c5ZjAwMDAwM29wZmRhZjVkZWNhIn0.example'}
            style={{ width: '100%', height: '100%' }}
            attributionControl={false}
          >
            {baliLocations.map((location) => (
              <Marker
                key={location.id}
                longitude={location.coordinates[0]}
                latitude={location.coordinates[1]}
                anchor="bottom"
              >
                <motion.div
                  className="cursor-pointer"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  onMouseEnter={() => setHoveredLocation(location)}
                  onMouseLeave={() => setHoveredLocation(null)}
                  onClick={() => onMarkerClick(location)}
                >
                  <div
                    className="w-8 h-8 rectangular border-2 border-white shadow-lg flex items-center justify-center"
                    style={{ backgroundColor: getMarkerColor(location.type) }}
                  >
                    <div className="w-3 h-3 bg-white rectangular"></div>
                  </div>
                </motion.div>
              </Marker>
            ))}

            {/* Hover Popup */}
            {hoveredLocation && Popup && (
              <Popup
                longitude={hoveredLocation.coordinates[0]}
                latitude={hoveredLocation.coordinates[1]}
                anchor="top"
                closeButton={false}
                closeOnClick={false}
                className="hover-popup"
              >
                <div className="p-2 text-center">
                  <h4 className="font-medium text-enterprise-brown">{hoveredLocation.name}</h4>
                  <p className="text-xs text-wood-light">{hoveredLocation.type}</p>
                </div>
              </Popup>
            )}
          </Map>
        ) : (
          <div className="flex items-center justify-center h-full bg-gradient-to-br from-temple/5 to-golden/5">
            <div className="text-center">
              <div className="text-4xl mb-4">🗺️</div>
              <h3 className="text-xl font-serif text-enterprise-brown mb-2">Mapa się ładuje...</h3>
              <p className="text-wood-light">Przygotowujemy interaktywną mapę Bali</p>
            </div>
          </div>
        )}

        {/* Map overlay with instructions */}
        {isMapReady && (
          <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rectangular p-3 text-sm text-wood-light">
            <p>🗺️ Kliknij na pin aby poznać szczegóły</p>
          </div>
        )}
      </ScrollReveal>

      {/* Location Details Modal */}
      <AnimatePresence>
        {selectedLocation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedLocation(null)}
          >
            <motion.div
              initial={{ scale: 0.9, y: 50 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 50 }}
              className="bg-white rectangular p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <h3 className="text-2xl font-serif text-enterprise-brown mb-2">
                  {selectedLocation.name}
                </h3>
                <p className="text-wood-light">{selectedLocation.description}</p>
              </div>

              {/* Location Image */}
              <div className="relative h-64 rectangular overflow-hidden mb-6">
                <img
                  src={selectedLocation.image}
                  alt={selectedLocation.name}
                  className="w-full h-full object-cover"
                />
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                {/* Highlights */}
                <div>
                  <h4 className="font-medium text-enterprise-brown mb-3">
                    🌟 Główne atrakcje
                  </h4>
                  <ul className="space-y-2">
                    {selectedLocation.highlights.map((highlight, index) => (
                      <li key={index} className="flex items-start gap-2 text-wood-light text-sm">
                        <span className="text-enterprise-brown mt-1">•</span>
                        {highlight}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Yoga Spots */}
                <div>
                  <h4 className="font-medium text-enterprise-brown mb-3">
                    🧘‍♀️ Miejsca do jogi
                  </h4>
                  <ul className="space-y-2">
                    {selectedLocation.yogaSpots.map((spot, index) => (
                      <li key={index} className="flex items-start gap-2 text-wood-light text-sm">
                        <span className="text-enterprise-brown mt-1">•</span>
                        {spot}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="flex justify-center pt-6 mt-6 border-t">
                <button
                  onClick={() => setSelectedLocation(null)}
                  className="btn-unified-primary"
                >
                  Zamknij
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Stats */}
      <ScrollReveal className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="text-center p-6 bg-enterprise-brown/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">6</div>
          <div className="text-wood-light text-sm">Magicznych miejsc</div>
        </div>
        <div className="text-center p-6 bg-golden/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">3</div>
          <div className="text-wood-light text-sm">Świątynie</div>
        </div>
        <div className="text-center p-6 bg-emerald-500/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">2</div>
          <div className="text-wood-light text-sm">Wodospady</div>
        </div>
        <div className="text-center p-6 bg-blue-500/5 rectangular">
          <div className="text-2xl font-bold text-enterprise-brown mb-2">1</div>
          <div className="text-wood-light text-sm">Rajska wyspa</div>
        </div>
      </ScrollReveal>
    </div>
  );
}
