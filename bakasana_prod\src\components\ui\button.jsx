'use client';
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva } from "class-variance-authority";
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-secondary font-light transition-all duration-normal focus-visible:outline-none focus-visible:shadow-focus disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        // BAKASANA Unified Buttons - Using design tokens
        primary: "bg-transparent border border-charcoal text-charcoal hover:bg-enterprise-brown hover:text-sanctuary hover:border-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        secondary: "bg-transparent border border-stone text-stone hover:bg-stone hover:text-sanctuary hover:border-stone font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        accent: "bg-transparent border border-enterprise-brown text-enterprise-brown hover:bg-enterprise-brown hover:text-sanctuary hover:border-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        ghost: "bg-transparent text-charcoal hover:bg-whisper hover:text-enterprise-brown font-secondary text-small font-light tracking-widest uppercase px-12 py-4 transition-all duration-normal",
        // Legacy variants for compatibility
        outline: "bg-transparent border border-stone/20 text-charcoal hover:bg-whisper hover:text-enterprise-brown transition-all duration-normal",
        hero: "bg-transparent border border-stone/10 text-charcoal hover:bg-whisper hover:text-enterprise-brown transition-all duration-normal",
      },
      size: {
        sm: "px-8 py-3 text-small",
        default: "px-12 py-4 text-small",
        lg: "px-16 py-5 text-caption",
        icon: "h-9 w-9 px-0",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

const Button = React.forwardRef(({ 
  className, 
  variant, 
  size, 
  asChild = false, 
  ...props 
}, ref) => {
  const Comp = asChild ? Slot : "button"
  return (
    <Comp
      className={cn(buttonVariants({ variant, size, className }))}
      ref={ref}
      {...props}
    />
  )
})
Button.displayName = "Button"

export { Button, buttonVariants }
