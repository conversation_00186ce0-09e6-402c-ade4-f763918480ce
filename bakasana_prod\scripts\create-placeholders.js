// <PERSON>ript to create placeholder images for Sri Lanka section
const fs = require('fs');
const path = require('path');

// Create placeholder SVG function
function createPlaceholderSVG(width, height, text, bgColor = '#E8E5E0', textColor = '#8B7355') {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${bgColor}"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" font-weight="300" 
          text-anchor="middle" dominant-baseline="middle" fill="${textColor}">
      ${text}
    </text>
  </svg>`;
}

// Placeholder images needed
const placeholders = [
  // Destination hero
  { path: 'public/images/destinations/srilanka-hero.webp', width: 1920, height: 1080, text: 'Sri Lanka Hero' },
  
  // Blog images
  { path: 'public/images/blog/srilanka-sigiriya.webp', width: 828, height: 620, text: 'Sigiriya Lion Rock' },
  { path: 'public/images/blog/srilanka-ayurveda.webp', width: 828, height: 620, text: 'Ayurveda Treatment' },
  
  // Program images
  { path: 'public/images/programs/srilanka/colombo-arrival.webp', width: 800, height: 600, text: 'Colombo Arrival' },
  { path: 'public/images/programs/srilanka/sigiriya.webp', width: 800, height: 600, text: 'Sigiriya Day' },
  { path: 'public/images/programs/srilanka/dambulla.webp', width: 800, height: 600, text: 'Dambulla Caves' },
  { path: 'public/images/programs/srilanka/kandy.webp', width: 800, height: 600, text: 'Kandy City' },
  { path: 'public/images/programs/srilanka/tea-plantation.webp', width: 800, height: 600, text: 'Tea Plantations' },
  { path: 'public/images/programs/srilanka/ella.webp', width: 800, height: 600, text: 'Ella Mountains' },
  { path: 'public/images/programs/srilanka/ella-relax.webp', width: 800, height: 600, text: 'Ella Relaxation' },
  { path: 'public/images/programs/srilanka/galle.webp', width: 800, height: 600, text: 'Galle Fort' },
  { path: 'public/images/programs/srilanka/south-coast.webp', width: 800, height: 600, text: 'South Coast' },
  { path: 'public/images/programs/srilanka/departure.webp', width: 800, height: 600, text: 'Departure' }
];

// Create placeholder files
placeholders.forEach(placeholder => {
  const dir = path.dirname(placeholder.path);
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Create SVG content
  const svgContent = createPlaceholderSVG(
    placeholder.width, 
    placeholder.height, 
    placeholder.text
  );
  
  // Save as SVG (will be converted to WebP later)
  const svgPath = placeholder.path.replace('.webp', '.svg');
  fs.writeFileSync(svgPath, svgContent);
  
  console.log(`Created placeholder: ${svgPath}`);
});

console.log('\n✅ All placeholder images created!');
console.log('\n📝 Next steps:');
console.log('1. Replace SVG placeholders with real WebP images');
console.log('2. Run image optimization: npm run optimize-images');
console.log('3. Test the website locally: npm run dev');
