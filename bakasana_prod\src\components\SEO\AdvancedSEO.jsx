'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { generateAdvancedMetadata, generateHreflangLinks } from '@/lib/advancedSEO';

export default function AdvancedSEO({ 
  title, 
  description, 
  keywords, 
  structuredData, 
  canonicalUrl,
  noIndex = false,
  imageUrl
}) {
  const pathname = usePathname();

  useEffect(() => {
    // Update page metadata dynamically
    if (title) {
      document.title = title;
    }

    // Update meta description
    if (description) {
      let metaDescription = document.querySelector('meta[name="description"]');
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.name = 'description';
        document.head.appendChild(metaDescription);
      }
      metaDescription.content = description;
    }

    // Update meta keywords
    if (keywords) {
      let metaKeywords = document.querySelector('meta[name="keywords"]');
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta');
        metaKeywords.name = 'keywords';
        document.head.appendChild(metaKeywords);
      }
      metaKeywords.content = keywords;
    }

    // Add canonical URL
    if (canonicalUrl) {
      let canonicalLink = document.querySelector('link[rel="canonical"]');
      if (!canonicalLink) {
        canonicalLink = document.createElement('link');
        canonicalLink.rel = 'canonical';
        document.head.appendChild(canonicalLink);
      }
      canonicalLink.href = canonicalUrl;
    }

    // Add hreflang links
    const hreflangLinks = generateHreflangLinks(pathname);
    hreflangLinks.forEach(link => {
      let hreflangLink = document.querySelector(`link[hreflang="${link.hreflang}"]`);
      if (!hreflangLink) {
        hreflangLink = document.createElement('link');
        hreflangLink.rel = 'alternate';
        hreflangLink.hreflang = link.hreflang;
        document.head.appendChild(hreflangLink);
      }
      hreflangLink.href = link.href;
    });

    // Add noindex if specified
    if (noIndex) {
      let robotsMeta = document.querySelector('meta[name="robots"]');
      if (!robotsMeta) {
        robotsMeta = document.createElement('meta');
        robotsMeta.name = 'robots';
        document.head.appendChild(robotsMeta);
      }
      robotsMeta.content = 'noindex, nofollow';
    }

    // Add Open Graph meta tags
    const ogTags = [
      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      { property: 'og:url', content: canonicalUrl },
      { property: 'og:type', content: 'website' },
      { property: 'og:locale', content: 'pl_PL' },
      { property: 'og:site_name', content: 'BAKASANA - Retreaty Jogi' },
    ];

    if (imageUrl) {
      ogTags.push(
        { property: 'og:image', content: imageUrl },
        { property: 'og:image:width', content: '1200' },
        { property: 'og:image:height', content: '630' },
        { property: 'og:image:alt', content: title }
      );
    }

    ogTags.forEach(tag => {
      let ogMeta = document.querySelector(`meta[property="${tag.property}"]`);
      if (!ogMeta) {
        ogMeta = document.createElement('meta');
        ogMeta.setAttribute('property', tag.property);
        document.head.appendChild(ogMeta);
      }
      ogMeta.content = tag.content;
    });

    // Add Twitter Card meta tags
    const twitterTags = [
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:site', content: '@bakasana_yoga' },
      { name: 'twitter:creator', content: '@bakasana_yoga' },
      { name: 'twitter:title', content: title },
      { name: 'twitter:description', content: description?.substring(0, 157) + '...' },
    ];

    if (imageUrl) {
      twitterTags.push({ name: 'twitter:image', content: imageUrl });
    }

    twitterTags.forEach(tag => {
      let twitterMeta = document.querySelector(`meta[name="${tag.name}"]`);
      if (!twitterMeta) {
        twitterMeta = document.createElement('meta');
        twitterMeta.name = tag.name;
        document.head.appendChild(twitterMeta);
      }
      twitterMeta.content = tag.content;
    });

    // Add structured data
    if (structuredData) {
      let structuredDataScript = document.querySelector('#structured-data-script');
      if (!structuredDataScript) {
        structuredDataScript = document.createElement('script');
        structuredDataScript.id = 'structured-data-script';
        structuredDataScript.type = 'application/ld+json';
        document.head.appendChild(structuredDataScript);
      }
      structuredDataScript.innerHTML = JSON.stringify(structuredData);
    }

    // Track SEO performance
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'seo_optimization', {
        event_category: 'SEO',
        event_label: pathname,
        custom_parameter_1: title?.length || 0,
        custom_parameter_2: description?.length || 0
      });
    }

  }, [title, description, keywords, canonicalUrl, noIndex, imageUrl, structuredData, pathname]);

  return null;
}

// Hook for tracking SEO performance
export function useSEOTracking() {
  const pathname = usePathname();

  useEffect(() => {
    // Track page performance metrics
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0];
      
      if (navigation && window.gtag) {
        window.gtag('event', 'page_performance', {
          event_category: 'Performance',
          event_label: pathname,
          custom_parameter_1: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
          custom_parameter_2: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)
        });
      }
    }

    // Track core web vitals
    if (typeof window !== 'undefined' && 'web-vitals' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(metric => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'CLS',
              value: Math.round(metric.value * 1000),
              custom_parameter_1: pathname
            });
          }
        });

        getFID(metric => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'FID',
              value: Math.round(metric.value),
              custom_parameter_1: pathname
            });
          }
        });

        getFCP(metric => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'FCP',
              value: Math.round(metric.value),
              custom_parameter_1: pathname
            });
          }
        });

        getLCP(metric => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'LCP',
              value: Math.round(metric.value),
              custom_parameter_1: pathname
            });
          }
        });

        getTTFB(metric => {
          if (window.gtag) {
            window.gtag('event', 'web_vitals', {
              event_category: 'Web Vitals',
              event_label: 'TTFB',
              value: Math.round(metric.value),
              custom_parameter_1: pathname
            });
          }
        });
      });
    }
  }, [pathname]);
}