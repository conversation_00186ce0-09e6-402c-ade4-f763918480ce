'use client';

import React from 'react';

const RetreatCalendar = ({ retreats }) => {
  const handleBooking = (retreatId) => {
    window.location.href = `/rezerwacja?retreat=${retreatId}`;
  };

  return (
    <div className="container mx-auto px-6 py-16">
      <div className="grid gap-12 max-w-4xl mx-auto">
        {retreats.map((retreat) => (
          <article
            key={retreat.id}
            className="bg-white p-8 border border-gray-100 hover:shadow-lg transition-shadow"
          >
            {/* Title */}
            <h3 className="text-2xl font-playfair text-charcoal mb-4">
              {retreat.title}
            </h3>

            {/* Date */}
            <p className="text-lg text-charcoal mb-2">
              {retreat.startDate} - {retreat.endDate}
            </p>

            {/* Location */}
            <p className="text-lg text-charcoal mb-4">
              {retreat.location}
            </p>

            {/* Description */}
            <p className="text-gray-600 mb-6 leading-relaxed">
              {retreat.description}
            </p>

            {/* Price */}
            <div className="mb-6">
              <span className="text-xl font-medium text-charcoal">
                {retreat.price}
              </span>
              {retreat.originalPrice && (
                <span className="text-gray-400 line-through ml-2">
                  {retreat.originalPrice}
                </span>
              )}
            </div>

            {/* Button */}
            <button
              onClick={() => handleBooking(retreat.id)}
              disabled={!retreat.available}
              className="w-full bg-charcoal text-white py-3 px-6 hover:bg-gold transition-colors disabled:opacity-50"
            >
              {retreat.available ? 'Zarezerwuj miejsce' : 'Brak miejsc'}
            </button>
          </article>
        ))}
      </div>
    </div>
  );
};

export default RetreatCalendar;