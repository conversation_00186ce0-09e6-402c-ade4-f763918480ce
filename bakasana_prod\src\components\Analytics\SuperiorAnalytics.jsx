'use client';

import { useEffect } from 'react';
import <PERSON>rip<PERSON> from 'next/script';
import { usePathname } from 'next/navigation';

const MIXPANEL_TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN;
const SENTRY_DSN = process.env.NEXT_PUBLIC_SENTRY_DSN;
const PLAUSIBLE_DOMAIN = process.env.NEXT_PUBLIC_PLAUSIBLE_DOMAIN;

export default function SuperiorAnalytics() {
  const pathname = usePathname();

  useEffect(() => {
    // Initialize Mixpanel
    if (MIXPANEL_TOKEN && typeof window !== 'undefined') {
      if (window.mixpanel) {
        window.mixpanel.track('Page View', {
          page: pathname,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          referrer: document.referrer,
          screen_resolution: `${screen.width}x${screen.height}`,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          device_type: /Mobi|Android/i.test(navigator.userAgent) ? 'mobile' : 'desktop',
          connection_type: navigator.connection?.effectiveType || 'unknown',
        });
      }
    }

    // Track advanced user behavior
    let scrollDepth = 0;
    let timeSpent = 0;
    let interactions = 0;
    let clicks = 0;
    let startTime = Date.now();

    const trackScrollDepth = () => {
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const trackingDepth = Math.round(((scrollTop + windowHeight) / documentHeight) * 100);
      
      if (trackingDepth > scrollDepth && trackingDepth % 25 === 0) {
        scrollDepth = trackingDepth;
        
        // Track in Mixpanel
        if (window.mixpanel) {
          window.mixpanel.track('Scroll Depth', {
            depth: scrollDepth,
            page: pathname,
            time_spent: Date.now() - startTime,
          });
        }
        
        // Track in GA4
        if (window.gtag) {
          window.gtag('event', 'scroll_depth', {
            event_category: 'Engagement',
            event_label: `${scrollDepth}%`,
            value: scrollDepth,
            custom_parameter_1: pathname,
            custom_parameter_2: Date.now() - startTime,
          });
        }
      }
    };

    const trackInteractions = (e) => {
      interactions++;
      if (e.type === 'click') {
        clicks++;
        
        // Track specific retreat interactions
        const target = e.target.closest('a, button');
        if (target) {
          const href = target.href || target.getAttribute('data-href');
          const text = target.textContent || target.getAttribute('aria-label');
          
          // Track retreat booking interest
          if (href && (href.includes('rezerwacja') || href.includes('booking'))) {
            if (window.mixpanel) {
              window.mixpanel.track('Booking Interest', {
                button_text: text,
                page: pathname,
                timestamp: new Date().toISOString(),
              });
            }
            
            if (window.gtag) {
              window.gtag('event', 'booking_interest', {
                event_category: 'Conversion',
                event_label: text,
                value: 1,
                custom_parameter_1: pathname,
              });
            }
          }
          
          // Track program interest
          if (href && href.includes('program')) {
            if (window.mixpanel) {
              window.mixpanel.track('Program Interest', {
                button_text: text,
                page: pathname,
                timestamp: new Date().toISOString(),
              });
            }
          }
          
          // Track contact interest
          if (href && href.includes('kontakt')) {
            if (window.mixpanel) {
              window.mixpanel.track('Contact Interest', {
                button_text: text,
                page: pathname,
                timestamp: new Date().toISOString(),
              });
            }
          }
        }
      }
    };

    const trackTimeSpent = () => {
      timeSpent = Date.now() - startTime;
      
      // Track time milestones
      if (timeSpent > 30000 && timeSpent < 31000) { // 30 seconds
        if (window.mixpanel) {
          window.mixpanel.track('Engaged User', {
            time_spent: timeSpent,
            page: pathname,
            interactions: interactions,
            scroll_depth: scrollDepth,
          });
        }
      }
      
      if (timeSpent > 120000 && timeSpent < 121000) { // 2 minutes
        if (window.mixpanel) {
          window.mixpanel.track('Highly Engaged User', {
            time_spent: timeSpent,
            page: pathname,
            interactions: interactions,
            scroll_depth: scrollDepth,
          });
        }
      }
    };

    const trackBeforeUnload = () => {
      const finalTimeSpent = Date.now() - startTime;
      
      if (window.mixpanel) {
        window.mixpanel.track('Page Exit', {
          time_spent: finalTimeSpent,
          page: pathname,
          interactions: interactions,
          clicks: clicks,
          scroll_depth: scrollDepth,
          timestamp: new Date().toISOString(),
        });
      }
      
      if (window.gtag) {
        window.gtag('event', 'page_exit', {
          event_category: 'Engagement',
          event_label: pathname,
          value: Math.round(finalTimeSpent / 1000),
          custom_parameter_1: interactions,
          custom_parameter_2: scrollDepth,
        });
      }
    };

    // Add event listeners
    window.addEventListener('scroll', trackScrollDepth, { passive: true });
    window.addEventListener('click', trackInteractions);
    window.addEventListener('keydown', trackInteractions);
    window.addEventListener('beforeunload', trackBeforeUnload);
    
    // Track time spent every 10 seconds
    const timeInterval = setInterval(trackTimeSpent, 10000);

    return () => {
      window.removeEventListener('scroll', trackScrollDepth);
      window.removeEventListener('click', trackInteractions);
      window.removeEventListener('keydown', trackInteractions);
      window.removeEventListener('beforeunload', trackBeforeUnload);
      clearInterval(timeInterval);
    };
  }, [pathname]);

  return (
    <>
      {/* Mixpanel */}
      {MIXPANEL_TOKEN && (
        <Script
          id="mixpanel-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,a){if(!a.__SV){var b=window;try{var d,m,j,k=b.location,f=k.hash;d=function(a,b){return(m=a.match(RegExp(b+"=([^&]*)")))?m[1]:null};f&&d(f,"state")&&(j=JSON.parse(decodeURIComponent(d(f,"state"))),"mpeditor"===j.action&&(b.sessionStorage.setItem("_mpcehash",f),history.replaceState(j.desiredHash||"",c.title,k.pathname+k.search)))}catch(n){}var l,h;window.mixpanel=a;a._i=[];a.init=function(b,d,g){function c(b,i){var a=i.split(".");2==a.length&&(b=b[a[0]],i=a[1]);b[i]=function(){b.push([i].concat(Array.prototype.slice.call(arguments,0)))}}var e=a;"undefined"!==typeof g?e=a[g]=[]:g="mixpanel";e.people=e.people||[];e.toString=function(b){var a="mixpanel";"mixpanel"!==g&&(a+="."+g);b||(a+=" (stub)");return a};e.people.toString=function(){return e.toString(1)+".people (stub)"};l="disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove".split(" ");for(h=0;h<l.length;h++)c(e,l[h]);a._i.push([b,d,g])};a.__SV=1.2;b=c.createElement("script");b.type="text/javascript";b.async=!0;b.src="undefined"!==typeof MIXPANEL_CUSTOM_LIB_URL?MIXPANEL_CUSTOM_LIB_URL:"file:"===c.location.protocol&&"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js".match(/^\\/\\//)?"https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js":"//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js";d=c.getElementsByTagName("script")[0];d.parentNode.insertBefore(b,d)}})(document,window.mixpanel||[]);
              mixpanel.init("${MIXPANEL_TOKEN}", {
                debug: false,
                track_pageview: false,
                persistence: 'localStorage',
                api_host: 'https://api-eu.mixpanel.com',
                loaded: function(mixpanel) {
                  console.log('Mixpanel loaded successfully');
                }
              });
            `,
          }}
        />
      )}

      {/* Sentry Error Tracking */}
      {SENTRY_DSN && (
        <Script
          id="sentry-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(e,n,t,r,o,a,s){e[o]=e[o]||function(){(e[o].q=e[o].q||[]).push(arguments)},a=n.createElement(t),s=n.getElementsByTagName(t)[0],a.async=1,a.src=r,s.parentNode.insertBefore(a,s)}(window,document,"script","https://browser.sentry-cdn.com/7.28.1/bundle.min.js","Sentry");
              Sentry.init({
                dsn: "${SENTRY_DSN}",
                environment: "${process.env.NODE_ENV}",
                tracesSampleRate: 1.0,
                integrations: [
                  new Sentry.BrowserTracing(),
                ],
                beforeSend(event, hint) {
                  // Filter out common non-critical errors
                  if (event.exception) {
                    const error = event.exception.values[0];
                    if (error.type === 'ChunkLoadError' || 
                        error.type === 'NetworkError' ||
                        error.value?.includes('Non-Error promise rejection')) {
                      return null;
                    }
                  }
                  return event;
                },
              });
            `,
          }}
        />
      )}

      {/* Plausible Analytics */}
      {PLAUSIBLE_DOMAIN && (
        <Script
          defer
          data-domain={PLAUSIBLE_DOMAIN}
          data-api="https://plausible.io/api/event"
          src="https://plausible.io/js/script.js"
          strategy="afterInteractive"
          onLoad={() => {
            // Track custom events with Plausible
            if (window.plausible) {
              window.plausible('pageview', {
                u: window.location.href,
                p: pathname,
              });
            }
          }}
        />
      )}

      {/* Custom Analytics Tracking */}
      <Script
        id="custom-analytics-tracking"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            // Global tracking functions
            window.trackRetreatBooking = function(retreatType, step, value) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Retreat Booking Step', {
                  retreat_type: retreatType,
                  step: step,
                  value: value,
                  timestamp: new Date().toISOString(),
                });
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'retreat_booking_step', {
                  event_category: 'Conversion',
                  event_label: retreatType + '_' + step,
                  value: value,
                  custom_parameter_1: retreatType,
                  custom_parameter_2: step,
                });
              }
              
              // Plausible
              if (window.plausible) {
                window.plausible('Retreat Booking', {
                  props: {
                    retreat_type: retreatType,
                    step: step,
                    value: value,
                  }
                });
              }
            };
            
            window.trackYogaPractice = function(practiceType, duration) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Yoga Practice', {
                  practice_type: practiceType,
                  duration: duration,
                  timestamp: new Date().toISOString(),
                });
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'yoga_practice', {
                  event_category: 'Engagement',
                  event_label: practiceType,
                  value: duration,
                });
              }
            };
            
            window.trackNewsletterSignup = function(location, email) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Newsletter Signup', {
                  location: location,
                  timestamp: new Date().toISOString(),
                });
                
                // Identify user
                if (email) {
                  window.mixpanel.identify(email);
                  window.mixpanel.people.set({
                    \$email: email,
                    \$last_seen: new Date().toISOString(),
                    signup_location: location,
                  });
                }
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'newsletter_signup', {
                  event_category: 'Lead',
                  event_label: location,
                  value: 1,
                });
              }
              
              // Plausible
              if (window.plausible) {
                window.plausible('Newsletter Signup', {
                  props: { location: location }
                });
              }
            };
            
            window.trackContactForm = function(formType, step) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Contact Form', {
                  form_type: formType,
                  step: step,
                  timestamp: new Date().toISOString(),
                });
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'contact_form', {
                  event_category: 'Lead',
                  event_label: formType + '_' + step,
                  value: step === 'submit' ? 1 : 0,
                });
              }
            };
            
            // Track video interactions
            window.trackVideoInteraction = function(videoId, action, position) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Video Interaction', {
                  video_id: videoId,
                  action: action,
                  position: position,
                  timestamp: new Date().toISOString(),
                });
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'video_' + action, {
                  event_category: 'Video',
                  event_label: videoId,
                  value: Math.round(position),
                });
              }
            };
            
            // Track image gallery interactions
            window.trackGalleryInteraction = function(imageId, action) {
              // Mixpanel
              if (window.mixpanel) {
                window.mixpanel.track('Gallery Interaction', {
                  image_id: imageId,
                  action: action,
                  timestamp: new Date().toISOString(),
                });
              }
              
              // GA4
              if (window.gtag) {
                window.gtag('event', 'gallery_' + action, {
                  event_category: 'Engagement',
                  event_label: imageId,
                  value: 1,
                });
              }
            };
          `,
        }}
      />
    </>
  );
}