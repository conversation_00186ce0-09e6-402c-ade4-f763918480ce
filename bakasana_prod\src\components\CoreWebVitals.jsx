'use client';

import { useEffect } from 'react';

export default function CoreWebVitals() {
  useEffect(() => {
    // Critical performance optimizations
    if (typeof window !== 'undefined') {
      // Optimize images loading
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.loading) {
          img.loading = 'lazy';
        }
      });

      // Optimize third-party scripts
      const scripts = document.querySelectorAll('script[src]');
      scripts.forEach(script => {
        if (script.src.includes('googletagmanager') || 
            script.src.includes('google-analytics') ||
            script.src.includes('hotjar') ||
            script.src.includes('clarity')) {
          script.async = true;
          script.defer = true;
        }
      });

      // Preload critical resources
      const preloadResources = [
        '/images/background/bali-hero.webp',
        '/images/logo/bakasana-logo.svg'
      ];

      preloadResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.webp') ? 'image' : 'image';
        document.head.appendChild(link);
      });

      // Font optimization handled by Next.js font system
      // Removed hardcoded font URLs to prevent 404 errors

      // Optimize CSS loading
      const criticalCSS = document.createElement('style');
      criticalCSS.textContent = `
        /* Critical CSS for above-the-fold content */
        body { 
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          font-display: swap;
          color: #2A2724;
          background-color: #FDFCF8;
          margin: 0;
          padding: 0;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        .hero-section {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F3 100%);
          will-change: transform;
          contain: layout style paint;
        }
        
        .navbar {
          position: fixed;
          top: 0;
          width: 100%;
          z-index: 1000;
          background: rgba(253, 252, 248, 0.95);
          backdrop-filter: blur(10px);
          will-change: transform;
          contain: layout style paint;
        }
        
        .main-content {
          will-change: transform;
          contain: layout style paint;
        }
      `;
      document.head.appendChild(criticalCSS);

      // Removed scroll optimization - was causing performance issues
      // Native scrolling is faster than custom optimization

      // Cleanup
      return () => {
        // No cleanup needed
      };
    }
  }, []);

  return null;
}