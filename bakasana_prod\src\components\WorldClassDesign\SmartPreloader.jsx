'use client';

import React, { useEffect, useState } from 'react';

/**
 * 🚀 SMART PRELOADER - TOP 1% DESIGN FEATURE
 * Inteligentny preloader z progresywnym ładowaniem
 * Inspirowane przez Apple, Linear.app, najlepsze SaaS
 */
const SmartPreloader = ({ 
  enabled = true,
  showProgress = true,
  minimumLoadTime = 1000,
  resources = [],
  onComplete = () => {},
  className = '',
  ...props 
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [loadedResources, setLoadedResources] = useState(0);
  const [currentTask, setCurrentTask] = useState('Inicjalizacja...');

  useEffect(() => {
    if (!enabled) {
      setIsLoading(false);
      onComplete();
      return;
    }

    const startTime = performance.now();
    let progressInterval;

    const loadResources = async () => {
      const tasks = [
        { name: 'Inicjalizacja...', duration: 200 },
        { name: 'Ładowanie komponentów...', duration: 300 },
        { name: 'Przygotowanie danych...', duration: 400 },
        { name: 'Optymalizacja...', duration: 200 },
        { name: 'Finalizowanie...', duration: 100 },
        ...resources.map(resource => ({
          name: `Ładowanie ${resource.name}...`,
          duration: resource.estimatedTime || 200,
          load: resource.load,
        })),
      ];

      let completedTasks = 0;
      
      for (const task of tasks) {
        setCurrentTask(task.name);
        
        if (task.load) {
          try {
            await task.load();
          } catch (error) {
            console.warn(`Failed to load ${task.name}:`, error);
          }
        } else {
          await new Promise(resolve => setTimeout(resolve, task.duration));
        }
        
        completedTasks++;
        setProgress((completedTasks / tasks.length) * 100);
        setLoadedResources(completedTasks);
      }

      // Ensure minimum load time
      const elapsedTime = performance.now() - startTime;
      if (elapsedTime < minimumLoadTime) {
        await new Promise(resolve => 
          setTimeout(resolve, minimumLoadTime - elapsedTime)
        );
      }

      setCurrentTask('Gotowe!');
      setProgress(100);
      
      // Fade out
      setTimeout(() => {
        setIsLoading(false);
        onComplete();
      }, 300);
    };

    loadResources();

    return () => {
      if (progressInterval) clearInterval(progressInterval);
    };
  }, [enabled, resources, minimumLoadTime, onComplete]);

  if (!enabled || !isLoading) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-rice via-sand-light to-ocean/20 ${className}`}
      style={{
        backdropFilter: 'blur(1px)',
        transition: 'opacity 0.3s ease-out',
      }}
      {...props}
    >
      <div className="text-center max-w-md mx-auto px-6">
        {/* Logo or brand */}
        <div className="mb-8">
          <div 
            className="w-16 h-16 mx-auto mb-4 rectangular bg-enterprise-brown/10 flex items-center justify-center"
            style={{
              animation: 'breathe 2s ease-in-out infinite',
            }}
          >
            <svg
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              className="text-enterprise-brown"
            >
              <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7L12 2z"/>
              <path d="M12 22V12"/>
              <path d="M7 12l5-5 5 5"/>
            </svg>
          </div>
          <h1 className="text-2xl font-light text-enterprise-brown tracking-wide">
            Bali Yoga Journey
          </h1>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="w-full bg-enterprise-brown/10 rectangular h-1 mb-4 overflow-hidden">
            <div
              className="bg-enterprise-brown h-1 rectangular transition-all duration-300 ease-out"
              style={{
                width: `${progress}%`,
                boxShadow: '0 0 8px rgba(124, 152, 133, 0.3)',
              }}
            />
          </div>
          
          {showProgress && (
            <div className="flex justify-between items-center text-sm text-enterprise-brown/70">
              <span>{currentTask}</span>
              <span>{Math.round(progress)}%</span>
            </div>
          )}
        </div>

        {/* Loading indicator */}
        <div className="flex items-center justify-center gap-2">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-enterprise-brown rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-enterprise-brown rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
            <div className="w-2 h-2 bg-enterprise-brown rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
          </div>
          <span className="text-xs text-enterprise-brown/60 font-light">
            {loadedResources > 0 && `${loadedResources} załadowanych`}
          </span>
        </div>

        {/* Inspirational message */}
        <div className="mt-8 text-center">
          <p className="text-sm text-enterprise-brown/60 font-light leading-relaxed">
            „Każda podróż zaczyna się od pierwszego kroku"
          </p>
        </div>
      </div>

      <style jsx>{`
        @keyframes breathe {
          0%, 100% { 
            transform: scale(1); 
            opacity: 0.8; 
          }
          50% { 
            transform: scale(1.05); 
            opacity: 1; 
          }
        }
        
        @media (prefers-reduced-motion: reduce) {
          div[style*="animation"] {
            animation: none !important;
          }
        }
      `}</style>
    </div>
  );
};

export default React.memo(SmartPreloader);