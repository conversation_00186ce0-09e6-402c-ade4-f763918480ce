// sanity/schemas/blogPost.js
export default {
  name: 'blogPost',
  title: 'Artykuły bloga',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Tytuł artykułu',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      title: 'URL (slug)',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'excerpt',
      title: 'Krótki opis (excerpt)',
      type: 'text',
      rows: 3,
      validation: Rule => Rule.max(300)
    },
    {
      name: 'content',
      title: 'Treść artykułu',
      type: 'array',
      of: [
        {
          type: 'block'
        },
        {
          type: 'image',
          options: {
            hotspot: true
          },
          fields: [
            {
              name: 'alt',
              title: 'Tekst alternatywny',
              type: 'string'
            },
            {
              name: 'caption',
              title: 'Podpis',
              type: 'string'
            }
          ]
        }
      ]
    },
    {
      name: 'mainImage',
      title: '<PERSON><PERSON><PERSON><PERSON> zdj<PERSON>',
      type: 'image',
      options: {
        hotspot: true
      },
      fields: [
        {
          name: 'alt',
          title: 'Tekst alternatywny',
          type: 'string'
        }
      ]
    },
    {
      name: 'author',
      title: 'Autor',
      type: 'reference',
      to: [{type: 'author'}]
    },
    {
      name: 'categories',
      title: 'Kategorie',
      type: 'array',
      of: [
        {
          type: 'reference',
          to: [{type: 'category'}]
        }
      ]
    },
    {
      name: 'tags',
      title: 'Tagi',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        layout: 'tags'
      }
    },
    {
      name: 'publishedAt',
      title: 'Data publikacji',
      type: 'datetime',
      validation: Rule => Rule.required()
    },
    {
      name: 'featured',
      title: 'Wyróżniony artykuł',
      type: 'boolean',
      initialValue: false
    },
    {
      name: 'seo',
      title: 'SEO',
      type: 'object',
      fields: [
        {
          name: 'metaTitle',
          title: 'Meta tytuł',
          type: 'string',
          validation: Rule => Rule.max(60)
        },
        {
          name: 'metaDescription',
          title: 'Meta opis',
          type: 'text',
          validation: Rule => Rule.max(160)
        },
        {
          name: 'keywords',
          title: 'Słowa kluczowe',
          type: 'array',
          of: [{type: 'string'}]
        }
      ]
    }
  ],
  preview: {
    select: {
      title: 'title',
      author: 'author.name',
      publishedAt: 'publishedAt',
      featured: 'featured',
      media: 'mainImage'
    },
    prepare(selection) {
      const {title, author, publishedAt, featured} = selection
      const date = publishedAt ? new Date(publishedAt).toLocaleDateString('pl-PL') : ''
      const status = featured ? '⭐ Wyróżniony' : ''
      
      return {
        title: title,
        subtitle: `${author || 'Brak autora'} | ${date}${status ? ' | ' + status : ''}`,
        media: selection.media
      }
    }
  },
  orderings: [
    {
      title: 'Najnowsze',
      name: 'newest',
      by: [
        {field: 'publishedAt', direction: 'desc'}
      ]
    },
    {
      title: 'Najstarsze',
      name: 'oldest',
      by: [
        {field: 'publishedAt', direction: 'asc'}
      ]
    },
    {
      title: 'Wyróżnione',
      name: 'featured',
      by: [
        {field: 'featured', direction: 'desc'},
        {field: 'publishedAt', direction: 'desc'}
      ]
    }
  ]
}
