#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔄 Przywracanie oryginalnej wersji BAKASANA...\n');

const restoreOriginal = () => {
  console.log('📋 Przywracanie oryginalnych plików...');
  
  const filesToRestore = [
    'src/app/layout.jsx',
    'src/app/page.jsx',
    'src/components/ConditionalNavbar.jsx'
  ];

  filesToRestore.forEach(file => {
    const originalPath = path.join(process.cwd(), file);
    const backupPath = path.join(process.cwd(), file.replace('.jsx', '.backup.jsx'));
    
    if (fs.existsSync(backupPath)) {
      fs.copyFileSync(backupPath, originalPath);
      console.log(`✅ Przywrócono: ${file.replace('.jsx', '.backup.jsx')} -> ${file}`);
    } else {
      console.log(`⚠️  Brak backupu dla: ${file}`);
    }
  });
};

const showRestoreInstructions = () => {
  console.log('\n✅ Przywracanie oryginalnej wersji zakończone!\n');
  console.log('📋 Możesz teraz uruchomić:');
  console.log('   npm run dev    - Oryginalna wersja development');
  console.log('   npm run build  - Oryginalna wersja build');
  console.log('   npm run start  - Oryginalna wersja production\n');
  
  console.log('🔄 Aby ponownie przełączyć na Premium:');
  console.log('   node scripts/switch-to-premium.js\n');
  
  console.log('📁 Pliki Premium nadal dostępne:');
  console.log('   ✨ src/app/premium-layout.jsx');
  console.log('   ✨ src/app/premium-page.jsx');
  console.log('   ✨ src/app/premium-demo/page.jsx');
  console.log('   ✨ src/components/Premium*.jsx');
  console.log('   ✨ src/app/premium-enhanced.css\n');
};

// Wykonaj przywracanie
try {
  restoreOriginal();
  showRestoreInstructions();
} catch (error) {
  console.error('❌ Błąd podczas przywracania:', error.message);
  process.exit(1);
}