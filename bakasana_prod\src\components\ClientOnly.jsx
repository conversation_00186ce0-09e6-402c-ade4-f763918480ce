'use client'; // Dyrektywa oznaczająca komponent kliencki

import React, { useState, useEffect } from 'react';

// Usuń wszelkie importy, które mogą tworzyć cykliczne zależności
// NIE importuj tutaj client-home.jsx ani komponentów, które importują ClientOnly

const ClientOnly = ({ children }) => {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return null; // Lub jaki<PERSON> placeholder/loader
  }

  return <>{children}</>;
};

export default ClientOnly;