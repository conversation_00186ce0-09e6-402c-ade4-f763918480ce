'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

const SpectacularHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const heroRef = useRef(null);

  // Simple scroll tracking
  const [titleOpacity, setTitleOpacity] = useState(1);
  const [titleY, setTitleY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);

    // Scroll listener for subtle parallax
    const handleScroll = () => {
      const scrolled = window.scrollY;
      setTitleOpacity(Math.max(0, 1 - scrolled / 400));
      setTitleY(-scrolled / 6);
    };

    window.addEventListener('scroll', handleScroll);

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Removed fireflies animation for clean old money aesthetic

  // Simple fade-in animation for old money elegance
  const getTitleStyle = () => ({
    opacity: isLoaded ? 1 : 0,
    transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
    transition: 'all 2s ease-out 0.5s'
  });

  const title = "BAKASANA";

  return (
    <section 
      ref={heroRef}
      className="relative h-screen overflow-hidden"
      style={{ height: '100vh' }}
    >
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi Bali i Sri Lanka"
          fill
          className="object-cover object-center"
          priority
          sizes="100vw"
          quality={95}
        />

        {/* Clean overlay for old money aesthetic */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/30" />
      </div>





      {/* Main Content */}
      <div
        className="absolute inset-0 z-50 flex flex-col items-center justify-center text-center"
        style={{
          opacity: titleOpacity,
          transform: `translateY(${titleY}px)`
        }}
      >
        {/* Main Title - Old Money Style */}
        <div className="mb-10 relative">
          <h1
            className="font-didot text-white font-light tracking-[0.3em]"
            style={{
              ...getTitleStyle(),
              fontSize: 'clamp(50px, 8vw, 90px)',
              textShadow: '0 2px 4px rgba(0,0,0,0.1)',
              lineHeight: '1.1'
            }}
          >
            {title}
          </h1>
        </div>

        {/* Subtitle - Old Money Style */}
        <p
          className="font-helvetica text-white font-light tracking-[0.15em]"
          style={{
            fontSize: '16px',
            opacity: isLoaded ? 0.7 : 0,
            transform: isLoaded ? 'translateY(0)' : 'translateY(20px)',
            transition: 'all 1.5s ease 1s',
            marginTop: '40px'
          }}
        >
          jóga jest drogą ciszy
        </p>
      </div>


    </section>
  );
};

export default SpectacularHero;
