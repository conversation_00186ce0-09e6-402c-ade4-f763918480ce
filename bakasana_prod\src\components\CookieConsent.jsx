'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function CookieConsentBanner() {
  const [showBanner, setShowBanner] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Sprawdź czy użytkownik już wyraził zgodę
    const consent = localStorage.getItem('bakasana-cookie-consent');
    if (!consent) {
      setShowBanner(true);
      // Pokaż banner z małym opóźnieniem dla lepszego UX
      setTimeout(() => setIsVisible(true), 1000);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('bakasana-cookie-consent', 'accepted');
    setIsVisible(false);
    setTimeout(() => setShowBanner(false), 300);

    // Włącz Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }
  };

  const handleDecline = () => {
    localStorage.setItem('bakasana-cookie-consent', 'declined');
    setIsVisible(false);
    setTimeout(() => setShowBanner(false), 300);

    // Wyłącz Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
  };

  if (!showBanner) return null;

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 z-50 transition-transform duration-300 ${
        isVisible ? 'translate-y-0' : 'translate-y-full'
      }`}
      style={{
        background: "rgba(139, 115, 85, 0.95)",
        backdropFilter: "blur(10px)",
        borderTop: "1px solid rgba(139, 115, 85, 0.2)"
      }}
    >
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <div className="flex-1 text-white">
            <p className="mb-2 text-sm">
              🍪 Ta strona używa plików cookies, aby zapewnić najlepsze doświadczenia użytkownika.
            </p>
            <p className="text-xs opacity-90">
              Używamy cookies do analizy ruchu i personalizacji treści.{' '}
              <Link
                href="/polityka-prywatnosci"
                className="underline hover:no-underline"
              >
                Dowiedz się więcej
              </Link>
            </p>
          </div>

          <div className="flex gap-3">
            <button
              onClick={handleDecline}
              className="px-4 py-2 text-sm text-white border border-white/30 rectangular hover:bg-white/10 transition-colors"
            >
              Odrzuć
            </button>
            <button
              onClick={handleAccept}
              className="px-6 py-2 text-sm bg-enterprise-brown text-white rectangular hover:bg-enterprise-brown/90 transition-colors shadow-lg"
            >
              Akceptuję
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
