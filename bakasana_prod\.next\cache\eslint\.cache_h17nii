[{"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js": "1", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js": "2", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js": "3", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js": "4", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js": "5", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js": "6", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js": "7", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js": "8", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js": "9", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js": "10", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js": "11", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js": "13", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js": "14", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js": "15", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js": "16", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js": "17", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js": "18", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx": "21", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx": "22", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx": "23", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js": "24", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx": "25", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js": "28", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js": "29", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js": "30", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js": "31", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js": "32", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js": "33", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js": "34", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js": "35", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\accessibilityTesting.js": "36", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js": "37", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js": "38", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoManager.js": "39", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js": "40", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredDataManager.js": "41", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js": "42", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js": "43", "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js": "44"}, {"size": 6440, "mtime": 1750136629000, "results": "45", "hashOfConfig": "46"}, {"size": 4750, "mtime": 1750228680000, "results": "47", "hashOfConfig": "46"}, {"size": 6263, "mtime": 1750228714000, "results": "48", "hashOfConfig": "46"}, {"size": 4150, "mtime": 1750227113000, "results": "49", "hashOfConfig": "46"}, {"size": 2352, "mtime": 1750227130000, "results": "50", "hashOfConfig": "46"}, {"size": 7630, "mtime": 1750227164000, "results": "51", "hashOfConfig": "46"}, {"size": 2591, "mtime": 1750223066000, "results": "52", "hashOfConfig": "46"}, {"size": 1555, "mtime": 1750137055000, "results": "53", "hashOfConfig": "46"}, {"size": 558, "mtime": 1750137055000, "results": "54", "hashOfConfig": "46"}, {"size": 755, "mtime": 1750137055000, "results": "55", "hashOfConfig": "46"}, {"size": 4688, "mtime": 1752410773610, "results": "56", "hashOfConfig": "46"}, {"size": 982, "mtime": 1753090998297, "results": "57", "hashOfConfig": "58"}, {"size": 3811, "mtime": 1752534114385, "results": "59", "hashOfConfig": "46"}, {"size": 2920, "mtime": 1750137055000, "results": "60", "hashOfConfig": "46"}, {"size": 2000, "mtime": 1752534592254, "results": "61", "hashOfConfig": "46"}, {"size": 2319, "mtime": 1752996879020, "results": "62", "hashOfConfig": "46"}, {"size": 6153, "mtime": 1752996810882, "results": "63", "hashOfConfig": "46"}, {"size": 1306, "mtime": 1750137055000, "results": "64", "hashOfConfig": "46"}, {"size": 5482, "mtime": 1752761132661, "results": "65", "hashOfConfig": "58"}, {"size": 11250, "mtime": 1753081106029, "results": "66", "hashOfConfig": "58"}, {"size": 9728, "mtime": 1753080625203, "results": "67", "hashOfConfig": "58"}, {"size": 47511, "mtime": 1753002170658, "results": "68", "hashOfConfig": "58"}, {"size": 34116, "mtime": 1753091173045, "results": "69", "hashOfConfig": "58"}, {"size": 13396, "mtime": 1752509953353, "results": "70", "hashOfConfig": "46"}, {"size": 8140, "mtime": 1753091243231, "results": "71", "hashOfConfig": "58"}, {"size": 2024, "mtime": 1753011837666, "results": "72", "hashOfConfig": "46"}, {"size": 1189, "mtime": 1752229991826, "results": "73", "hashOfConfig": "46"}, {"size": 17127, "mtime": 1750189264000, "results": "74", "hashOfConfig": "46"}, {"size": 1147, "mtime": 1750137055000, "results": "75", "hashOfConfig": "46"}, {"size": 1901, "mtime": 1750137056000, "results": "76", "hashOfConfig": "46"}, {"size": 1700, "mtime": 1752558963287, "results": "77", "hashOfConfig": "46"}, {"size": 11592, "mtime": 1752669072282, "results": "78", "hashOfConfig": "46"}, {"size": 18399, "mtime": 1752417314631, "results": "79", "hashOfConfig": "46"}, {"size": 11007, "mtime": 1752789380820, "results": "80", "hashOfConfig": "46"}, {"size": 2033, "mtime": 1750137056000, "results": "81", "hashOfConfig": "46"}, {"size": 10489, "mtime": 1752996051268, "results": "82", "hashOfConfig": "46"}, {"size": 15647, "mtime": 1752532937438, "results": "83", "hashOfConfig": "46"}, {"size": 19639, "mtime": 1752789179363, "results": "84", "hashOfConfig": "46"}, {"size": 10890, "mtime": 1752996663790, "results": "85", "hashOfConfig": "46"}, {"size": 10182, "mtime": 1750224902000, "results": "86", "hashOfConfig": "46"}, {"size": 11204, "mtime": 1752996721307, "results": "87", "hashOfConfig": "46"}, {"size": 2224, "mtime": 1752757322917, "results": "88", "hashOfConfig": "46"}, {"size": 4983, "mtime": 1752413498649, "results": "89", "hashOfConfig": "46"}, {"size": 1951, "mtime": 1752826470161, "results": "90", "hashOfConfig": "46"}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "lcw2q5", {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19x6zoz", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\lib\\sanity.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\bookings\\[id]\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\login\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\admin\\verify\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\booking\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\api\\newsletter\\route.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\blog\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\galeria\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\old-money\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\retreaty\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\robots.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\sitemap.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\metadata.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\About\\OldMoneyAbout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Footer\\OldMoneyFooter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Hero\\OldMoneyHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Performance\\EnterprisePerformanceDashboard.tsx", ["223", "224", "225"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\EnterprisePWAInstaller.tsx", ["226"], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PWA\\ServiceWorker.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\Services\\OldMoneyServices.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\WorldClassDesign\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\blogPosts.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\contactData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\eventData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\navigationLinks.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\programData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\data\\retreatsData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useAdvancedAnimations.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\hooks\\useInView.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\accessibilityTesting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\advancedSEO.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoAutomation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\seoManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\structuredDataManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\lib\\yogaStructuredData.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\middleware.js", [], [], {"ruleId": "227", "severity": 1, "message": "228", "line": 562, "column": 6, "nodeType": "229", "endLine": 562, "endColumn": 19, "suggestions": "230"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 581, "column": 6, "nodeType": "229", "endLine": 581, "endColumn": 20, "suggestions": "232"}, {"ruleId": "227", "severity": 1, "message": "228", "line": 586, "column": 6, "nodeType": "229", "endLine": 586, "endColumn": 20, "suggestions": "233"}, {"ruleId": "227", "severity": 1, "message": "234", "line": 291, "column": 6, "nodeType": "229", "endLine": 291, "endColumn": 8, "suggestions": "235"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'collectMetrics'. Either include it or remove the dependency array.", "ArrayExpression", ["236"], "React Hook useCallback has a missing dependency: 'updateMetrics'. Either include it or remove the dependency array.", ["237"], ["238"], "React Hook useCallback has missing dependencies: 'shouldShowInstallPrompt' and 'trackInstallation'. Either include them or remove the dependency array.", ["239"], {"desc": "240", "fix": "241"}, {"desc": "242", "fix": "243"}, {"desc": "244", "fix": "245"}, {"desc": "246", "fix": "247"}, "Update the dependencies array to be: [autoRefresh, collectMetrics]", {"range": "248", "text": "249"}, "Update the dependencies array to be: [startPolling, updateMetrics]", {"range": "250", "text": "251"}, "Update the dependencies array to be: [collectMetrics, startPolling]", {"range": "252", "text": "253"}, "Update the dependencies array to be: [shouldShowInstallPrompt, trackInstallation]", {"range": "254", "text": "255"}, [16233, 16246], "[autoRefresh, collectMetrics]", [16795, 16809], "[startPolling, updateMetrics]", [16915, 16929], "[collectMetrics, startPolling]", [7665, 7667], "[shouldShowInstallPrompt, trackInstallation]"]