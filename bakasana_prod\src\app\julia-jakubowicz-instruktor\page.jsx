// Metadata export for Next.js
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Award, BookOpen, Heart, MapPin, Users, Star, Clock, 
  Phone, Mail, Instagram, Check, ArrowRight, Calendar, 
  Globe, Target, Trophy, Shield, Zap, Coffee
} from 'lucide-react';

export const metadata = {
  title: '<PERSON> - Certyfikowana Instruktorka Jogi | Ekspert Retreatów Bali & Sri Lanka',
  description: '🏆 <PERSON> - certyfikowana instruktorka jogi (200h YTT), fiz<PERSON><PERSON><PERSON><PERSON>ka, ekspert retreatów Bali i Sri Lanka. 8 lat doświadczenia, 127 zadowolonych klientów. 4.9/5 ⭐',
  keywords: 'julia j<PERSON> joga, certyfikowana instruktorka jogi, ekspert retreatów, yoga teacher training, fizjoterapeutka joga, instruktor jogi bali, sri lanka yoga expert',
  openGraph: {
    title: '<PERSON> - <PERSON>rty<PERSON>wana Instruktorka Jogi | BAKASANA',
    description: '🏆 <PERSON> - certyfikowana instruktorka jogi, ekspert retreatów Bali i Sri Lanka. 8 lat doświadczenia, 4.9/5 ⭐',
    images: ['/images/about/julia-jakubowicz-instruktor.jpg'],
  },
  alternates: {
    canonical: 'https://bakasana-travel.blog/julia-jakubowicz-instruktor',
  },
};

const JuliaJakubowiczInstruktor = () => {
  const achievements = [
    {
      icon: <Award className="w-6 h-6 text-temple" />,
      title: "200h YTT Certyfikat",
      description: "Yoga Teacher Training - międzynarodowy certyfikat",
      year: "2016"
    },
    {
      icon: <Heart className="w-6 h-6 text-temple" />,
      title: "Fizjoterapeutka",
      description: "Magister fizjoterapii - holistyczne podejście",
      year: "2014"
    },
    {
      icon: <Globe className="w-6 h-6 text-temple" />,
      title: "127 Zadowolonych Klientów",
      description: "Przeprowadzonych retreatów w Azji",
      year: "2017-2024"
    },
    {
      icon: <Target className="w-6 h-6 text-temple" />,
      title: "Ekspert Retreatów",
      description: "Specjalistka od Bali i Sri Lanka",
      year: "2017-teraz"
    }
  ];

  const specializations = [
    {
      name: "Hatha Yoga",
      description: "Tradycyjna praktyka dla wszystkich poziomów",
      icon: "🧘‍♀️"
    },
    {
      name: "Vinyasa Flow",
      description: "Dynamiczna sekwencja połączona z oddechem",
      icon: "🌊"
    },
    {
      name: "Medytacja",
      description: "Mindfulness i techniki kontemplacyjne",
      icon: "🧠"
    },
    {
      name: "Pranayama",
      description: "Techniki oddechowe i kontrola energii",
      icon: "💨"
    },
    {
      name: "Restorative Yoga",
      description: "Regenerująca praktyka z supportami",
      icon: "🛌"
    },
    {
      name: "Ayurveda",
      description: "Podstawy ayurvedyjskiej filozofii zdrowia",
      icon: "🌿"
    }
  ];

  const retreatStats = [
    { number: "127", label: "Zadowolonych klientów" },
    { number: "4.9", label: "Średnia ocena" },
    { number: "8", label: "Lat doświadczenia" },
    { number: "15", label: "Krajów zwiedzanych" }
  ];

  const testimonials = [
    {
      name: "Anna Kowalska",
      text: "Julia to nie tylko świetna instruktorka jogi, ale przede wszystkim mądra przewodniczka duchowa. Jej podejście do każdego uczestnika jest indywidualne i pełne ciepła.",
      rating: 5,
      retreat: "Bali Retreat 2024"
    },
    {
      name: "Marcin Nowak",
      text: "Profesjonalizm Julii w połączeniu z jej wiedzą medyczną jako fizjoterapeutki daje niesamowite rezultaty. Polecam każdemu!",
      rating: 5,
      retreat: "Sri Lanka Retreat 2023"
    },
    {
      name: "Katarzyna Wiśniewska",
      text: "Dzięki Julii odkryłam prawdziwą moc jogi. Jej retreaty to transformacyjne doświadczenie na wszystkich poziomach.",
      rating: 5,
      retreat: "Bali Retreat 2024"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-temple/10 to-golden/10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4 bg-temple/20 text-temple border-temple/30">
                Certyfikowana Instruktorka Jogi
              </Badge>
              
              <h1 className="text-4xl md:text-5xl font-bold text-charcoal mb-6">
                Julia Jakubowicz
                <span className="block text-temple text-3xl md:text-4xl">
                  Ekspert Retreatów Jogi
                </span>
              </h1>
              
              <p className="text-xl text-wood mb-8 max-w-xl">
                Certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka. 
                8 lat doświadczenia w prowadzeniu transformacyjnych retreatów 
                na Bali i Sri Lanka.
              </p>
              
              <div className="flex flex-wrap gap-4 mb-8">
                <div className="flex items-center gap-2 bg-white/80 px-4 py-2 rounded-full">
                  <Star className="w-4 h-4 text-golden fill-golden" />
                  <span className="font-semibold text-charcoal">4.9/5</span>
                  <span className="text-wood">127 opinii</span>
                </div>
                <div className="flex items-center gap-2 bg-white/80 px-4 py-2 rounded-full">
                  <Award className="w-4 h-4 text-temple" />
                  <span className="text-charcoal">200h YTT</span>
                </div>
                <div className="flex items-center gap-2 bg-white/80 px-4 py-2 rounded-full">
                  <Heart className="w-4 h-4 text-temple" />
                  <span className="text-charcoal">Fizjoterapeutka</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg" 
                  className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
                  asChild
                >
                  <Link href="/rezerwacja">
                    Rezerwuj Retreat
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </Button>
                
                <Button 
                  size="lg" 
                  variant="outline" 
                  className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
                  asChild
                >
                  <Link href="/kontakt">
                    Skontaktuj się
                  </Link>
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative z-10 rounded-2xl overflow-hidden">
                <Image
                  src="/images/about/julia-main.webp"
                  alt="Julia Jakubowicz - Certyfikowana Instruktorka Jogi"
                  width={600}
                  height={700}
                  className="object-cover w-full h-[600px]"
                  priority
                />
              </div>
              
              {/* Floating stats */}
              <div className="absolute top-8 -left-8 z-20 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-temple">127</div>
                  <div className="text-sm text-wood">Zadowolonych klientów</div>
                </div>
              </div>
              
              <div className="absolute bottom-8 -right-8 z-20 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-temple">4.9★</div>
                  <div className="text-sm text-wood">Średnia ocena</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Kwalifikacje i Osiągnięcia
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Profesjonalne przygotowanie i wieloletnie doświadczenie 
              gwarantują najwyższą jakość prowadzonych retreatów.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {achievements.map((achievement, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-temple/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    {achievement.icon}
                  </div>
                  <Badge variant="secondary" className="mb-2 bg-temple/10 text-temple text-xs">
                    {achievement.year}
                  </Badge>
                  <h3 className="font-semibold text-charcoal mb-2">{achievement.title}</h3>
                  <p className="text-sm text-wood">{achievement.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Specializations */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Specjalizacje Jogi
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Szerokie spectrum praktyk jogi dostosowanych do indywidualnych 
              potrzeb i poziomu zaawansowania uczestników.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {specializations.map((spec, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20 hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-3xl">{spec.icon}</div>
                    <h3 className="font-semibold text-charcoal">{spec.name}</h3>
                  </div>
                  <p className="text-wood text-sm">{spec.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-16 bg-temple/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            {retreatStats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-5xl font-bold text-temple mb-2">{stat.number}</div>
                <div className="text-wood font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Personal Story */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-charcoal mb-6">
                Moja Historia z Jogą
              </h2>
              
              <div className="space-y-6 text-wood">
                <p>
                  Podróż z jogą rozpoczęłam w 2014 roku, kiedy to ukończyłam studia 
                  z fizjoterapii. Połączenie wiedzy medycznej z praktyką jogi 
                  otworzyło przede mną nowe perspektywy holistycznego podejścia 
                  do zdrowia i well-being.
                </p>
                
                <p>
                  W 2016 roku otrzymałam certyfikat 200h YTT (Yoga Teacher Training), 
                  który pozwolił mi profesjonalnie prowadzić zajęcia jogi. Rok później 
                  odbyłam pierwszą podróż na Bali, która totalnie zmieniła moje życie.
                </p>
                
                <p>
                  Od 2017 roku organizuję retreaty jogi w najpiękniejszych miejscach 
                  Azji. Przez 8 lat przeprowadziłam setki godzin praktyki z ludźmi 
                  z całego świata, pomagając im w transformacji i odkrywaniu 
                  własnego potencjału.
                </p>
                
                <p>
                  Obecnie specialyzuję się w retreatach na Bali i Sri Lanka, 
                  łącząc praktykę jogi z odkrywaniem kultury, duchowości i 
                  naturalnego piękna tych niezwykłych miejsc.
                </p>
              </div>
              
              <div className="mt-8 flex flex-wrap gap-4">
                <Badge className="bg-temple/10 text-temple">
                  Fizjoterapeutka
                </Badge>
                <Badge className="bg-temple/10 text-temple">
                  200h YTT
                </Badge>
                <Badge className="bg-temple/10 text-temple">
                  Ekspert Bali
                </Badge>
                <Badge className="bg-temple/10 text-temple">
                  Sri Lanka Specialist
                </Badge>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/about/julia-teaching.webp"
                    alt="Julia Jakubowicz prowadzi zajęcia jogi"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/about/julia-meditation.webp"
                    alt="Julia Jakubowicz podczas medytacji"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/about/julia-bali.webp"
                    alt="Julia Jakubowicz na Bali"
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src="/images/about/julia-srilanka.webp"
                    alt="Julia Jakubowicz na Sri Lanka"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-sanctuary">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-charcoal mb-4">
              Co mówią uczestnicy o Julii?
            </h2>
            <p className="text-wood max-w-2xl mx-auto">
              Autentyczne opinie osób, które doświadczyły transformacji 
              pod opieką Julii podczas retreatów jogi.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/80 backdrop-blur-sm border-temple/20">
                <CardContent className="p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-golden fill-golden" />
                    ))}
                  </div>
                  <p className="text-wood mb-6 italic">"{testimonial.text}"</p>
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-charcoal">{testimonial.name}</p>
                        <p className="text-sm text-wood">{testimonial.retreat}</p>
                      </div>
                      <Badge variant="secondary" className="bg-temple/10 text-temple text-xs">
                        Verified
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-temple/10 to-golden/10">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-4">
            Gotowy na transformację z Julią?
          </h2>
          <p className="text-wood mb-8 max-w-2xl mx-auto">
            Dołącz do grupy 127 zadowolonych uczestników, którzy doświadczyli 
            transformacji podczas retreatów z certyfikowaną instruktorką Julią Jakubowicz.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Button 
              size="lg" 
              className="bg-temple hover:bg-temple/90 text-lg px-8 py-6"
              asChild
            >
              <Link href="/rezerwacja">
                Zarezerwuj Retreat
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6"
              asChild
            >
              <Link href="/program">
                Zobacz Program
              </Link>
            </Button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-wood">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <span>+48 666 777 888</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <Instagram className="w-4 h-4" />
              <span>@fly_with_bakasana</span>
            </div>
          </div>
        </div>
      </section>

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Person",
            "name": "Julia Jakubowicz",
            "jobTitle": "Certyfikowana Instruktorka Jogi",
            "description": "Certyfikowana instruktorka jogi (200h YTT) i fizjoterapeutka specjalizująca się w retreatach jogi na Bali i Sri Lanka",
            "url": "https://bakasana-travel.blog/julia-jakubowicz-instruktor",
            "image": "https://bakasana-travel.blog/images/about/julia-jakubowicz-instruktor.jpg",
            "telephone": "+48666777888",
            "email": "<EMAIL>",
            "address": {
              "@type": "PostalAddress",
              "addressCountry": "PL"
            },
            "worksFor": {
              "@type": "Organization",
              "name": "BAKASANA",
              "url": "https://bakasana-travel.blog"
            },
            "hasCredential": [
              {
                "@type": "EducationalOccupationalCredential",
                "name": "200h Yoga Teacher Training",
                "credentialCategory": "certification"
              },
              {
                "@type": "EducationalOccupationalCredential", 
                "name": "Magister Fizjoterapii",
                "credentialCategory": "degree"
              }
            ],
            "knowsAbout": [
              "Hatha Yoga",
              "Vinyasa Yoga",
              "Meditation",
              "Pranayama",
              "Ayurveda",
              "Yoga Retreats",
              "Physiotherapy"
            ],
            "sameAs": [
              "https://www.instagram.com/fly_with_bakasana/",
              "https://www.facebook.com/bakasana.yoga"
            ],
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.9",
              "reviewCount": "127",
              "bestRating": "5"
            }
          })
        }}
      />
    </div>
  );
};

export default JuliaJakubowiczInstruktor;