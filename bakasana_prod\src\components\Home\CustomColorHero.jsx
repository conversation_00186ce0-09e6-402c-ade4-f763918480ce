'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const CustomColorHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);
    
    // Parallax effect
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section 
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{ backgroundColor: '#F5E6D3' }}
    >
      {/* Background Image with Parallax */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          transform: `translateY(${scrollY * 0.4}px)`,
        }}
      >
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi w Bali i Sri Lanka"
          fill
          className="object-cover object-center"
          priority
          sizes="100vw"
          quality={90}
        />
        
        {/* Custom Color Overlay */}
        <div 
          className="absolute inset-0"
          style={{
            background: `linear-gradient(to bottom, 
              rgba(245, 230, 211, 0.7), 
              rgba(245, 230, 211, 0.5), 
              rgba(245, 230, 211, 0.8)
            )`
          }}
        />
        <div 
          className="absolute inset-0"
          style={{
            background: `linear-gradient(to right, 
              rgba(139, 69, 19, 0.1), 
              transparent, 
              rgba(139, 69, 19, 0.1)
            )`
          }}
        />
      </div>

      {/* Main Content */}
      <div className={`relative z-20 text-center max-w-6xl mx-auto px-8 lg:px-12 transition-all duration-1200 ease-out ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-20'
      }`}>
        
        {/* Elegant Label */}
        <div className={`inline-flex items-center gap-4 px-10 py-4 mb-10 backdrop-blur-sm border shadow-lg transition-all duration-800 delay-200 hover:shadow-xl ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: 'rgba(139, 69, 19, 0.2)'
        }}>
          <span 
            className="w-2.5 h-2.5 rectangular animate-pulse"
            style={{ backgroundColor: '#8B4513' }}
          ></span>
          <span 
            className="text-sm font-serif tracking-[0.3em] uppercase font-medium"
            style={{ color: '#8B4513' }}
          >
            RETREAT 2021 • Bali & Sri Lanka
          </span>
        </div>

        {/* Main Title - Old Money Style */}
        <h1 className={`font-didot font-light leading-[1.1] tracking-[0.3em] mb-8 transition-all duration-[2000ms] delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'
        }`}
        style={{ 
          fontSize: 'clamp(50px, 8vw, 90px)',
          color: '#8B4513',
          textShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          BAKASANA
        </h1>

        {/* Subtitle - Old Money Style */}
        <p className={`font-helvetica font-light tracking-[0.15em] mb-10 transition-all duration-[1500ms] delay-1000 ${
          isLoaded ? 'opacity-70 translate-y-0' : 'opacity-0 translate-y-5'
        }`}
        style={{ 
          fontSize: '16px',
          marginTop: '40px',
          color: 'rgba(139, 69, 19, 0.7)'
        }}>
          jóga jest drogą ciszy
        </p>

        {/* Description */}
        <p className={`text-lg md:text-xl lg:text-2xl max-w-4xl mx-auto leading-relaxed font-sans font-light mb-16 transition-all duration-1200 delay-700 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
        }`}
        style={{ 
          color: 'rgba(139, 69, 19, 0.7)',
          textShadow: '0 1px 2px rgba(139, 69, 19, 0.1)'
        }}>
          Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
        </p>

        {/* Statistics Grid */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 lg:gap-12 max-w-5xl mx-auto mb-16 transition-all duration-1200 delay-800 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
        }`}>
          {[
            { number: '24+', label: 'miejsca' },
            { number: '10', label: 'lat doświadczenia' },
            { number: '500+', label: 'zadowolonych uczestników' },
            { number: 'A+', label: 'ocena satysfakcji' }
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div 
                className="text-4xl md:text-5xl lg:text-6xl font-serif font-medium mb-3 group-hover:scale-110 transition-all duration-500"
                style={{ 
                  color: '#8B4513',
                  textShadow: '0 2px 4px rgba(139, 69, 19, 0.2)'
                }}
              >
                {stat.number}
              </div>
              <div 
                className="text-sm md:text-base lg:text-lg font-sans tracking-wide"
                style={{ color: 'rgba(139, 69, 19, 0.6)' }}
              >
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Buttons */}
        <div className={`flex flex-col sm:flex-row gap-8 justify-center items-center transition-all duration-1200 delay-1000 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'
        }`}>
          {/* Ghost Button */}
          <Link
            href="/program"
            className="group inline-flex items-center gap-4 px-12 py-5 bg-transparent border-2 font-sans font-medium tracking-wide transition-all duration-500 hover:shadow-xl"
            style={{
              color: '#8B4513',
              borderColor: 'rgba(139, 69, 19, 0.4)',
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#8B4513';
              e.target.style.color = '#FFFFFF';
              e.target.style.borderColor = '#8B4513';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.color = '#8B4513';
              e.target.style.borderColor = 'rgba(139, 69, 19, 0.4)';
            }}
          >
            <span>Przegląd harmonogramu</span>
            <svg className="w-5 h-5 transition-transform group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
            </svg>
          </Link>
          
          {/* Filled Button */}
          <Link
            href="/rezerwacja"
            className="group inline-flex items-center gap-4 px-12 py-5 font-sans font-medium tracking-wide transition-all duration-500 hover:shadow-xl hover:scale-105"
            style={{
              backgroundColor: '#8B4513',
              color: '#FFFFFF'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'rgba(139, 69, 19, 0.9)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#8B4513';
            }}
          >
            <span>Rezerwuj</span>
            <svg className="w-5 h-5 transition-transform group-hover:translate-x-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none overflow-hidden">
        {[
          { top: '25%', left: '25%', size: '12px', delay: '0s', duration: '8s' },
          { top: '33%', right: '33%', size: '10px', delay: '2s', duration: '10s' },
          { bottom: '33%', left: '33%', size: '14px', delay: '4s', duration: '9s' },
          { top: '66%', right: '25%', size: '8px', delay: '1s', duration: '11s' },
          { top: '50%', left: '16%', size: '10px', delay: '3s', duration: '7s' },
          { bottom: '25%', right: '16%', size: '12px', delay: '5s', duration: '12s' }
        ].map((particle, index) => (
          <div 
            key={index}
            className="absolute rectangular"
            style={{
              ...particle,
              width: particle.size,
              height: particle.size,
              backgroundColor: 'rgba(139, 69, 19, 0.2)',
              animation: `float ${particle.duration} ease-in-out infinite`,
              animationDelay: particle.delay,
              boxShadow: '0 2px 4px rgba(139, 69, 19, 0.1)'
            }}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className={`absolute bottom-12 left-1/2 transform -translate-x-1/2 z-20 transition-all duration-1200 delay-1200 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
      }`}>
        <div className="flex flex-col items-center group cursor-pointer">
          <span 
            className="text-xs mb-4 font-sans tracking-[0.3em] uppercase font-light group-hover:opacity-100 transition-opacity duration-300"
            style={{ color: 'rgba(139, 69, 19, 0.6)' }}
          >
            Odkryj więcej
          </span>
          <div 
            className="w-px h-12 animate-pulse"
            style={{ backgroundColor: 'rgba(139, 69, 19, 0.3)' }}
          ></div>
          <svg 
            className="w-6 h-6 mt-3 animate-bounce group-hover:scale-110 transition-transform duration-300" 
            fill="none" 
            stroke="rgba(139, 69, 19, 0.6)" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
};

export default CustomColorHero;