# Bakasana Travel Blog - Retreaty Jogowe na Bali

Strona internetowa dla retreatów jogowych na Bali prowadzonych przez Juli<PERSON> - magistra fizjoterapii i certyfikowaną instruktorkę jogi RYT 500.

🌐 **Live:** [https://bakasana-travel.blog](https://bakasana-travel.blog)

## 🧘‍♀️ O Projekcie

Elegancka strona internetowa prezentująca transformacyjne retreaty jogowe na Bali. Łączy praktykę jogi z odkrywaniem najpiękniejszych miejsc magicznej wyspy Indonezji.

### ✨ Funkcjonalności

- **Responsywny design** inspirowany kolorami Bali
- **SEO zoptymalizowane** z metadanymi i strukturalnymi danymi
- **Galeria zdjęć** z najpiękniejszych miejsc Bali
- **Blog jogowy** z artykułami o praktyce i wellness
- **Program retreatu** - szczegółowy 12-dniowy plan
- **Formularz kontaktowy** z integracją social media
- **Delikatne animacje** i płynne przejścia

### 🎨 Design System

- **Kolory:** Inspirowane naturą Bali (temple, golden, sage, lotus, mist)
- **Typografia:** Eleganckie fonty serif dla nagłówków
- **Komponenty:** Delikatne karty, przyciski z subtelnymi efektami
- **Animacje:** Gentle fade-in, hover effects, floating elements

## 🚀 Technologie

- **Next.js 15** - React framework z App Router
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animacje i przejścia
- **Lucide React** - Ikony
- **Sharp** - Optymalizacja obrazów
- **Next-sitemap** - Automatyczne generowanie sitemap

## 🛠️ Instalacja i Uruchomienie

```bash
# Klonowanie repozytorium
git clone [repository-url]
cd bakasana-travel-blog

# Instalacja zależności
npm install

# Uruchomienie serwera deweloperskiego
npm run dev
```

Otwórz [http://localhost:3002](http://localhost:3002) w przeglądarce.

## 📁 Struktura Projektu

```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── page.jsx           # Strona główna
│   ├── program/           # Program retreatu
│   ├── o-mnie/           # O instruktorce
│   ├── galeria/          # Galeria zdjęć
│   ├── blog/             # Blog jogowy
│   ├── kontakt/          # Formularz kontaktowy
│   ├── globals.css       # Style globalne
│   ├── metadata.js       # Funkcje SEO
│   ├── sitemap.js        # Sitemap
│   └── robots.js         # Robots.txt
├── components/           # Komponenty React
│   ├── Footer/          # Stopka
│   └── ui/              # Komponenty UI
├── data/                # Dane statyczne
│   ├── blogPosts.js     # Artykuły blog
│   └── programData.js   # Program retreatu
└── public/              # Pliki statyczne
    └── images/          # Zdjęcia i grafiki
```

## 🎯 SEO i Performance

- **Lighthouse Score:** 95+ we wszystkich kategoriach
- **Core Web Vitals:** Zoptymalizowane
- **Structured Data:** JSON-LD dla lepszego SEO
- **Meta Tags:** Open Graph, Twitter Cards
- **Sitemap:** Automatycznie generowany
- **Robots.txt:** Skonfigurowany dla crawlerów

## 📱 Responsywność

- **Mobile First:** Zaprojektowane dla urządzeń mobilnych
- **Breakpoints:** sm, md, lg, xl, 2xl
- **Touch Friendly:** Odpowiednie rozmiary przycisków
- **Performance:** Optymalizowane obrazy i lazy loading

## 🌍 Deployment

```bash
# Build produkcyjny
npm run build

# Start serwera produkcyjnego
npm start

# Analiza bundle'a
npm run build:analyze
```

## 📞 Kontakt

**Julia Jakubowicz**
- 📧 Email: <EMAIL>
- 📱 Telefon: +48 606 101 523
- 📍 Lokalizacja: Rzeszów, Polska
- 🌐 Studio: [flywithbakasana.pl](https://flywithbakasana.pl)

### Social Media
- [Instagram](https://www.instagram.com/fly_with_bakasana)
- [Facebook](https://www.facebook.com/p/Fly-with-bakasana-100077568306563/)
- [Fitssey](https://app.fitssey.com/Flywithbakasana/frontoffice)

## 📄 Licencja

© 2024 Fly with Bakasana. Wszystkie prawa zastrzeżone.
