import { NextResponse } from 'next/server';

// ConvertKit API configuration
const CONVERTKIT_API_KEY = process.env.CONVERTKIT_API_KEY;
const CONVERTKIT_FORM_ID = process.env.CONVERTKIT_FORM_ID;
const CONVERTKIT_API_URL = 'https://api.convertkit.com/v3';

export async function POST(request) {
  try {
    const { email, tags = [], source = 'website' } = await request.json();

    // Validate email
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { success: false, error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Check if ConvertKit is configured
    if (!CONVERTKIT_API_KEY || !CONVERTKIT_FORM_ID) {
      console.warn('ConvertKit not configured, simulating success');
      
      // In development or if ConvertKit not configured, just return success
      return NextResponse.json({
        success: true,
        message: 'Newsletter signup successful (simulated)',
        email
      });
    }

    // Subscribe to ConvertKit
    const convertKitResponse = await fetch(
      `${CONVERTKIT_API_URL}/forms/${CONVERTKIT_FORM_ID}/subscribe`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: CONVERTKIT_API_KEY,
          email,
          tags,
          fields: {
            source,
            signup_date: new Date().toISOString(),
            website: 'bakasana-travel.blog'
          }
        }),
      }
    );

    const convertKitData = await convertKitResponse.json();

    if (!convertKitResponse.ok) {
      throw new Error(convertKitData.message || 'ConvertKit API error');
    }

    // Log successful signup (for analytics)
    console.log('Newsletter signup:', {
      email,
      tags,
      source,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter',
      subscriber_id: convertKitData.subscription?.subscriber?.id
    });

  } catch (error) {
    console.error('Newsletter signup error:', error);

    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to subscribe to newsletter',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Newsletter API is working',
    configured: !!(CONVERTKIT_API_KEY && CONVERTKIT_FORM_ID),
    timestamp: new Date().toISOString()
  });
}
