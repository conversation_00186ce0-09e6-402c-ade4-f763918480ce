'use client';

/**
 * ♿ COLOR BLIND FILTERS - SVG Filters for Color Vision Deficiency Support
 * 
 * Provides SVG filters to simulate and assist with:
 * - Protanopia (red-blind)
 * - Deuteranopia (green-blind) 
 * - Tritanopia (blue-blind)
 * 
 * Based on research by <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON> (2009)
 * "A Physiologically-based Model for Simulation of Color Vision Deficiency"
 */

const ColorBlindFilters = () => {
  return (
    <svg
      style={{ position: 'absolute', width: 0, height: 0, overflow: 'hidden' }}
      aria-hidden="true"
    >
      <defs>
        {/* Protanopia Filter (Red-blind) */}
        <filter id="protanopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.567 0.433 0.000 0.000 0.000
                    0.558 0.442 0.000 0.000 0.000
                    0.000 0.242 0.758 0.000 0.000
                    0.000 0.000 0.000 1.000 0.000"
          />
        </filter>

        {/* Deuteranopia Filter (Green-blind) */}
        <filter id="deuteranopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.625 0.375 0.000 0.000 0.000
                    0.700 0.300 0.000 0.000 0.000
                    0.000 0.300 0.700 0.000 0.000
                    0.000 0.000 0.000 1.000 0.000"
          />
        </filter>

        {/* Tritanopia Filter (Blue-blind) */}
        <filter id="tritanopia-filter">
          <feColorMatrix
            type="matrix"
            values="0.950 0.050 0.000 0.000 0.000
                    0.000 0.433 0.567 0.000 0.000
                    0.000 0.475 0.525 0.000 0.000
                    0.000 0.000 0.000 1.000 0.000"
          />
        </filter>

        {/* Enhanced Contrast Filter */}
        <filter id="enhanced-contrast">
          <feComponentTransfer>
            <feFuncA type="discrete" tableValues="0 .5 1"/>
          </feComponentTransfer>
        </filter>

        {/* High Contrast Filter */}
        <filter id="high-contrast">
          <feColorMatrix
            type="matrix"
            values="1.5 0 0 0 -0.25
                    0 1.5 0 0 -0.25
                    0 0 1.5 0 -0.25
                    0 0 0 1 0"
          />
        </filter>

        {/* Brightness Enhancement */}
        <filter id="brightness-enhance">
          <feComponentTransfer>
            <feFuncR type="gamma" amplitude="1" exponent="0.8"/>
            <feFuncG type="gamma" amplitude="1" exponent="0.8"/>
            <feFuncB type="gamma" amplitude="1" exponent="0.8"/>
          </feComponentTransfer>
        </filter>
      </defs>
    </svg>
  );
};

export default ColorBlindFilters;
