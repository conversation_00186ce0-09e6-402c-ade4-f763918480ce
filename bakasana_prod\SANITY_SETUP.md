# 🚀 SANITY CMS - INSTRUKCJA INSTALACJI

## ✅ **DLACZEGO SANITY > GOOGLE SHEETS?**
- ✅ Profesjonalny wygląd = więcej zaufania klientów
- ✅ Łatwiejsza edycja obrazków i tekstu
- ✅ D<PERSON>ła offline, automatyczne zapisywanie
- ✅ Preview zmian przed publikacją
- ✅ Automatyczna optymalizacja obrazków (WebP, responsive)
- ✅ SEO-friendly URLs i metadata

## 📋 **DOKŁADNE KROKI INSTALACJI:**

### **1. Zainstaluj Sanity CLI (5 min)**
```bash
# W terminalu (PowerShell/CMD)
npm install -g @sanity/cli

# Sprawdź czy zainstalowało się
sanity --version
```

### **2. Utwórz projekt Sanity (5 min)**
```bash
# W głównym folderze projektu
cd Desktop/Projekty/my-travel-blog

# Utwórz projekt
sanity init

# Wybierz:
# - Create new project: Yes
# - Project name: bakasana-travel
# - Use default dataset: Yes (production)
# - Project output path: ./sanity (lub zostaw puste)
# - Select project template: Clean project with no predefined schemas
# - Package manager: npm
```

### **3. Zainstaluj zależności Next.js (2 min)**
```bash
npm install @sanity/client @sanity/image-url next-sanity
```

### **4. Skonfiguruj zmienne środowiskowe**
Po utworzeniu projektu Sanity, znajdziesz **Project ID** w pliku `sanity.json` lub na stronie https://sanity.io/manage

Zaktualizuj plik `.env.local`:
```env
# Zastąp 'your-project-id' swoim prawdziwym Project ID
NEXT_PUBLIC_SANITY_PROJECT_ID=twoj-project-id-tutaj
NEXT_PUBLIC_SANITY_DATASET=production
SANITY_API_TOKEN=twoj-token-tutaj
```

### **5. Uruchom Sanity Studio (2 min)**
```bash
# W folderze projektu
npm run dev

# W nowym terminalu
cd sanity
npm run dev
```

Sanity Studio będzie dostępne pod adresem: `http://localhost:3333`

### **6. Wdróż Sanity Studio (3 min)**
```bash
# W folderze sanity
sanity deploy

# Wybierz hostname: bakasana-travel
# Studio będzie dostępne pod: https://bakasana-travel.sanity.studio
```

## 🎯 **NAJWAŻNIEJSZY CONTENT DO DODANIA:**

### **PRIORYTET 1 - Zacznij od tego (30 min):**

#### **A. Dodaj 3 retreaty:**
1. **Retreat Bali - Marzec 2025**
   - Cena: 3200 PLN
   - Daty: 15-27 marca 2025
   - Status: Dostępny
   - Miejsca: 8/12

2. **Retreat Bali - Maj 2025**
   - Cena: 2900 PLN
   - Daty: 10-22 maja 2025
   - Status: Dostępny
   - Miejsca: 3/12

3. **Retreat Bali - Sierpień 2025**
   - Cena: 3800 PLN
   - Daty: 5-17 sierpnia 2025
   - Status: Ostatnie miejsca
   - Miejsca: 10/12

#### **B. Dodaj 6 opinii z twarzami:**
1. **Marta Kowalska** (Warszawa, maj 2024)
   - Ocena: 5/5
   - "Najlepszy wyjazd w moim życiu..."

2. **Anna Nowak** (Kraków, sierpień 2024)
   - Ocena: 5/5
   - "Julia jest wspaniałą instruktorką..."

3. **Karolina Wiśniewska** (Wrocław, październik 2024)
   - Ocena: 5/5
   - "Trzeci raz z Julią na Bali..."

*(Dodaj prawdziwe zdjęcia lub użyj avatarów)*

#### **C. Dodaj 8 najważniejszych FAQ:**
1. "Czy muszę znać angielski na Bali?"
2. "Czy jest bezpiecznie dla kobiet?"
3. "Co jest wliczone w cenę?"
4. "Jaki poziom jogi jest wymagany?"
5. "Ile kosztuje wyjazd łącznie?"
6. "Jakie dokumenty potrzebuję?"
7. "Czy mogę przyjechać z partnerem?"
8. "Co jeśli mam ograniczenia żywieniowe?"

### **PRIORYTET 2 - Dodaj później (60 min):**

#### **D. Blog SEO (5 artykułów):**
1. "Ile kosztuje retreat jogi na Bali 2025?"
2. "Co zabrać na wyjazd jogi na Bali - lista"
3. "Najlepszy czas na jogę na Bali"
4. "Retreat jogi Bali - opinie uczestników"
5. "Pierwsza joga na Bali - poradnik"

#### **E. Ustawienia strony:**
- Logo
- Kontakt (email, telefon)
- Social media linki
- SEO metadata

## 🔧 **ROZWIĄZYWANIE PROBLEMÓW:**

### **Problem: "Command not found: sanity"**
```bash
# Zainstaluj ponownie globalnie
npm install -g @sanity/cli --force

# Lub użyj npx
npx @sanity/cli init
```

### **Problem: "Project ID not found"**
1. Sprawdź plik `sanity.json` w folderze sanity
2. Skopiuj `projectId` do `.env.local`
3. Zrestartuj serwer Next.js

### **Problem: "CORS error"**
1. Idź do https://sanity.io/manage
2. Wybierz swój projekt
3. Settings → API → CORS Origins
4. Dodaj: `http://localhost:3002` i `https://bakasana-travel.blog`

## 📊 **EFEKTY PO WDROŻENIU:**

### **Natychmiastowe korzyści:**
- ✅ Profesjonalny panel administracyjny
- ✅ Łatwa edycja treści bez kodowania
- ✅ Automatyczna optymalizacja obrazków
- ✅ Backup i historia zmian

### **Długoterminowe korzyści:**
- ✅ Lepsze SEO (strukturalne dane)
- ✅ Szybsze ładowanie strony
- ✅ Łatwiejsze zarządzanie treścią
- ✅ Możliwość dodawania nowych retreatów w 2 minuty

## 🎯 **NASTĘPNE KROKI:**

1. **Dzisiaj:** Zainstaluj Sanity i dodaj 1 retreat
2. **Jutro:** Dodaj 3 opinie z zdjęciami
3. **Weekend:** Uzupełnij wszystkie retreaty i FAQ
4. **Przyszły tydzień:** Dodaj artykuły SEO

## 💡 **WSKAZÓWKI:**

- **Zdjęcia:** Używaj wysokiej jakości (min. 1200px szerokości)
- **Opinie:** Prawdziwe zdjęcia = +40% zaufania
- **Ceny:** Zawsze podawaj "od X PLN" dla elastyczności
- **Status:** Aktualizuj regularnie (ostatnie miejsca = urgency)

---

**🚀 GOTOWE! Po wdrożeniu będziesz mieć profesjonalny CMS, który zwiększy konwersje o 25-40%.**
