'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowDownTrayIcon, 
  XMarkIcon, 
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  ShareIcon,
  PlusIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

const PWAInstall = ({ 
  className = '',
  variant = 'banner', // banner, modal, button, inline
  showOnDesktop = true,
  showOnMobile = true,
  autoShow = true,
  onInstall,
  onDismiss
}) => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [deviceType, setDeviceType] = useState('desktop');
  const [browserType, setBrowserType] = useState('chrome');
  const [installStatus, setInstallStatus] = useState('idle'); // idle, installing, installed, error

  useEffect(() => {
    // Detect device type
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    setDeviceType(isMobile ? 'mobile' : 'desktop');

    // Detect browser type
    const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
    const isFirefox = /Firefox/.test(navigator.userAgent);
    const isSafari = /Safari/.test(navigator.userAgent) && /Apple Computer/.test(navigator.vendor);
    const isEdge = /Edge/.test(navigator.userAgent);
    
    if (isChrome) setBrowserType('chrome');
    else if (isFirefox) setBrowserType('firefox');
    else if (isSafari) setBrowserType('safari');
    else if (isEdge) setBrowserType('edge');

    // Check if app is already installed
    const checkInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches) {
        setIsInstalled(true);
      }
    };

    checkInstalled();

    // Listen for beforeinstallprompt
    const handleBeforeInstallPrompt = (event) => {
      event.preventDefault();
      setDeferredPrompt(event);
      setIsInstallable(true);
      
      if (autoShow && shouldShowPrompt()) {
        setShowPrompt(true);
      }
    };

    // Listen for appinstalled
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowPrompt(false);
      setInstallStatus('installed');
      console.log('PWA was installed');
      
      if (onInstall) onInstall();
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [autoShow, onInstall]);

  const shouldShowPrompt = () => {
    // Check if user has dismissed the prompt recently
    const lastDismissed = localStorage.getItem('pwa-install-dismissed');
    if (lastDismissed) {
      const dismissTime = new Date(lastDismissed);
      const now = new Date();
      const daysSinceDismiss = (now - dismissTime) / (1000 * 60 * 60 * 24);
      
      if (daysSinceDismiss < 7) {
        return false;
      }
    }

    // Check device type preference
    if (deviceType === 'mobile' && !showOnMobile) return false;
    if (deviceType === 'desktop' && !showOnDesktop) return false;

    // Check if already installed
    if (isInstalled) return false;

    return true;
  };

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      showManualInstallInstructions();
      return;
    }

    setInstallStatus('installing');

    try {
      const result = await deferredPrompt.prompt();
      
      if (result.outcome === 'accepted') {
        setInstallStatus('installed');
        setShowPrompt(false);
        console.log('User accepted the install prompt');
      } else {
        setInstallStatus('idle');
        console.log('User dismissed the install prompt');
      }
    } catch (error) {
      setInstallStatus('error');
      console.error('Error during installation:', error);
    }

    setDeferredPrompt(null);
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('pwa-install-dismissed', new Date().toISOString());
    
    if (onDismiss) onDismiss();
  };

  const showManualInstallInstructions = () => {
    const instructions = getManualInstallInstructions();
    
    // Create modal with instructions
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
      <div class="bg-white p-6 rectangular max-w-md mx-4">
        <h3 class="text-lg font-semibold mb-4">Install Bakasana Travel</h3>
        <div class="space-y-3 text-sm text-gray-600">
          ${instructions.map(step => `<div class="flex items-start gap-2">
            <div class="w-5 h-5 bg-temple-gold rectangular flex items-center justify-center text-white text-xs font-bold">${step.number}</div>
            <div>${step.text}</div>
          </div>`).join('')}
        </div>
        <button class="mt-4 w-full bg-temple-gold text-white py-2 px-4 rounded hover:bg-temple-gold/90">
          Got it!
        </button>
      </div>
    `;
    
    modal.querySelector('button').addEventListener('click', () => {
      document.body.removeChild(modal);
    });
    
    document.body.appendChild(modal);
  };

  const getManualInstallInstructions = () => {
    const baseInstructions = {
      chrome: [
        { number: 1, text: 'Click the menu button (three dots) in the top right corner' },
        { number: 2, text: 'Select "Install Bakasana Travel..."' },
        { number: 3, text: 'Click "Install" in the popup' }
      ],
      firefox: [
        { number: 1, text: 'Click the menu button (three lines) in the top right corner' },
        { number: 2, text: 'Select "Install this site as an app"' },
        { number: 3, text: 'Click "Install" to confirm' }
      ],
      safari: [
        { number: 1, text: 'Click the Share button in the browser toolbar' },
        { number: 2, text: 'Scroll down and tap "Add to Home Screen"' },
        { number: 3, text: 'Tap "Add" to confirm' }
      ],
      edge: [
        { number: 1, text: 'Click the menu button (three dots) in the top right corner' },
        { number: 2, text: 'Select "Apps" > "Install this site as an app"' },
        { number: 3, text: 'Click "Install" to confirm' }
      ]
    };

    return baseInstructions[browserType] || baseInstructions.chrome;
  };

  const getInstallButtonText = () => {
    switch (installStatus) {
      case 'installing':
        return 'Installing...';
      case 'installed':
        return 'Installed';
      case 'error':
        return 'Try Again';
      default:
        return 'Install App';
    }
  };

  const getInstallButtonIcon = () => {
    switch (installStatus) {
      case 'installing':
        return <div className="w-4 h-4 border-2 border-white border-t-transparent rectangular animate-spin" />;
      case 'installed':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <ArrowDownTrayIcon className="w-4 h-4" />;
    }
  };

  // Don't show if already installed
  if (isInstalled) {
    return null;
  }

  // Don't show if device type doesn't match preferences
  if (deviceType === 'mobile' && !showOnMobile) return null;
  if (deviceType === 'desktop' && !showOnDesktop) return null;

  // Button variant
  if (variant === 'button') {
    return (
      <button
        onClick={handleInstallClick}
        disabled={installStatus === 'installing' || installStatus === 'installed'}
        className={`
          inline-flex items-center gap-2 px-4 py-2 bg-temple-gold text-white rectangular
          hover:bg-temple-gold/90 transition-colors
          disabled:opacity-50 disabled:cursor-not-allowed
          ${className}
        `}
      >
        {getInstallButtonIcon()}
        {getInstallButtonText()}
      </button>
    );
  }

  // Inline variant
  if (variant === 'inline') {
    return (
      <div className={`bg-sanctuary p-4 rectangular border border-temple-gold/20 ${className}`}>
        <div className="flex items-center gap-4">
          <div className="flex-shrink-0">
            {deviceType === 'mobile' ? (
              <DevicePhoneMobileIcon className="w-8 h-8 text-temple-gold" />
            ) : (
              <ComputerDesktopIcon className="w-8 h-8 text-temple-gold" />
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="font-medium text-charcoal mb-1">
              Install Bakasana Travel
            </h3>
            <p className="text-sm text-stone">
              Get instant access to retreats, offline guides, and travel tools
            </p>
          </div>
          
          <button
            onClick={handleInstallClick}
            disabled={installStatus === 'installing' || installStatus === 'installed'}
            className="flex items-center gap-2 px-4 py-2 bg-temple-gold text-white rectangular hover:bg-temple-gold/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {getInstallButtonIcon()}
            Install
          </button>
        </div>
      </div>
    );
  }

  // Banner variant
  if (variant === 'banner' && showPrompt) {
    return (
      <div className={`fixed bottom-0 left-0 right-0 bg-white border-t border-stone-light shadow-2xl z-50 ${className}`}>
        <div className="max-w-4xl mx-auto p-4">
          <div className="flex items-center gap-4">
            <div className="flex-shrink-0">
              <img
                src="/icon-72x72.png"
                alt="Bakasana Travel"
                className="w-12 h-12 rectangular"
              />
            </div>
            
            <div className="flex-1">
              <h3 className="font-cormorant font-medium text-charcoal mb-1">
                Install Bakasana Travel
              </h3>
              <p className="text-sm text-stone">
                Access your travel guides offline, get retreat notifications, and enjoy a native app experience
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={handleDismiss}
                className="text-stone hover:text-charcoal transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleInstallClick}
                disabled={installStatus === 'installing' || installStatus === 'installed'}
                className="flex items-center gap-2 px-6 py-2 bg-temple-gold text-white rectangular hover:bg-temple-gold/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {getInstallButtonIcon()}
                {getInstallButtonText()}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modal variant
  if (variant === 'modal' && showPrompt) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rectangular max-w-md mx-4 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <img
                src="/icon-72x72.png"
                alt="Bakasana Travel"
                className="w-16 h-16 rectangular"
              />
              <div>
                <h3 className="font-cormorant text-lg font-medium text-charcoal">
                  Install Bakasana Travel
                </h3>
                <p className="text-sm text-stone">
                  For the best experience
                </p>
              </div>
            </div>
            
            <div className="space-y-3 mb-6">
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="w-5 h-5 text-sage-green" />
                <span className="text-sm text-charcoal">Access travel guides offline</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="w-5 h-5 text-sage-green" />
                <span className="text-sm text-charcoal">Get retreat notifications</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="w-5 h-5 text-sage-green" />
                <span className="text-sm text-charcoal">Fast, native app experience</span>
              </div>
              <div className="flex items-center gap-3">
                <CheckCircleIcon className="w-5 h-5 text-sage-green" />
                <span className="text-sm text-charcoal">Works on all devices</span>
              </div>
            </div>
          </div>
          
          <div className="flex bg-sanctuary">
            <button
              onClick={handleDismiss}
              className="flex-1 px-6 py-3 text-stone hover:text-charcoal transition-colors"
            >
              Maybe Later
            </button>
            <button
              onClick={handleInstallClick}
              disabled={installStatus === 'installing' || installStatus === 'installed'}
              className="flex-1 px-6 py-3 bg-temple-gold text-white hover:bg-temple-gold/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {getInstallButtonIcon()}
              {getInstallButtonText()}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default PWAInstall;