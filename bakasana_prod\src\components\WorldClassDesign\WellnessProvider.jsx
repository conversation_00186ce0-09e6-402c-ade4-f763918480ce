'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

/**
 * 🧘 WELLNESS PROVIDER - WARM MINIMALISM FOR BALI YOGA
 * Zachowuje accessibility i performance, usuwa tech-heavy efekty
 * Inspirowane briefem: ciepły minimalizm, wellness feeling
 */

export const WellnessContext = createContext({});

export const useWellness = () => {
  const context = useContext(WellnessContext);
  if (!context) {
    throw new Error('useWellness must be used within WellnessProvider');
  }
  return context;
};

const WellnessProvider = ({ 
  children,
  config = {},
  className = '',
  ...props 
}) => {
  const [isEnabled, setIsEnabled] = useState(true);
  const [features, setFeatures] = useState({
    // ZACHOWANE: Istotne dla funkcjonalności
    accessibilityEnhancements: true,
    performanceMonitoring: true,
    smoothScrollIndicator: true,
    
    // USUNIĘTE: Tech-heavy efekty
    ambientBackground: false,
    contextualCursor: false,
    keyboardShortcuts: false,
    progressIndicator: false,
    
    // NOWE: Wellness-specific
    breathingAnimations: true,
    simpleFadeIn: true,
    ...config.features,
  });

  // Accessibility state (zachowane)
  const [accessibilityMode, setAccessibilityMode] = useState({
    reducedMotion: false,
    highContrast: false,
    dyslexiaMode: false,
    focusVisible: false,
  });

  // Performance monitoring (zachowane, ale uproszczone)
  const [performanceMetrics, setPerformanceMetrics] = useState({
    loadTime: 0,
    firstPaint: 0,
  });

  // Initialize accessibility monitoring (zachowane)
  useEffect(() => {
    if (!features.accessibilityEnhancements) return;

    const checkAccessibilityPreferences = () => {
      setAccessibilityMode({
        reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        highContrast: window.matchMedia('(prefers-contrast: high)').matches,
        dyslexiaMode: localStorage.getItem('dyslexia-mode') === 'true',
        focusVisible: false,
      });
    };

    checkAccessibilityPreferences();

    // Listen for changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
    ];

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', checkAccessibilityPreferences);
    });

    // Focus visible detection
    const handleKeyDown = (e) => {
      if (e.key === 'Tab') {
        setAccessibilityMode(prev => ({ ...prev, focusVisible: true }));
      }
    };

    const handleMouseDown = () => {
      setAccessibilityMode(prev => ({ ...prev, focusVisible: false }));
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', checkAccessibilityPreferences);
      });
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [features.accessibilityEnhancements]);

  // Apply accessibility styles (zachowane)
  useEffect(() => {
    const applyAccessibilityStyles = () => {
      const style = document.createElement('style');
      style.id = 'wellness-accessibility';
      
      let css = '';
      
      if (accessibilityMode.reducedMotion) {
        css += `
          *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
            scroll-behavior: auto !important;
          }
        `;
      }
      
      if (accessibilityMode.highContrast) {
        css += `
          * {
            filter: contrast(1.2) !important;
          }
        `;
      }
      
      if (accessibilityMode.dyslexiaMode) {
        css += `
          * {
            font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;
          }
        `;
      }
      
      if (accessibilityMode.focusVisible) {
        css += `
          :focus {
            outline: 2px solid #7C9885 !important;
            outline-offset: 2px !important;
          }
        `;
      }
      
      style.textContent = css;
      
      const existingStyle = document.getElementById('wellness-accessibility');
      if (existingStyle) {
        existingStyle.remove();
      }
      
      if (css) {
        document.head.appendChild(style);
      }
    };

    applyAccessibilityStyles();
  }, [accessibilityMode]);

  // Breathing animations CSS (nowe)
  useEffect(() => {
    if (!features.breathingAnimations) return;

    const style = document.createElement('style');
    style.id = 'wellness-breathing';
    style.textContent = `
      .wellness-breathing {
        animation: gentle-breathing 6s ease-in-out infinite;
      }
      
      @keyframes gentle-breathing {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }
      
      .wellness-fade-in {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.8s ease-out;
      }
      
      .wellness-fade-in.visible {
        opacity: 1;
        transform: translateY(0);
      }
    `;
    
    document.head.appendChild(style);
    
    return () => {
      const existingStyle = document.getElementById('wellness-breathing');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [features.breathingAnimations]);

  const contextValue = {
    isEnabled,
    setIsEnabled,
    features,
    setFeatures,
    performanceMetrics,
    accessibilityMode,
    setAccessibilityMode,
    toggleFeature: (featureName) => {
      setFeatures(prev => ({
        ...prev,
        [featureName]: !prev[featureName],
      }));
    },
    toggleDyslexiaMode: () => {
      const newMode = !accessibilityMode.dyslexiaMode;
      localStorage.setItem('dyslexia-mode', newMode.toString());
      setAccessibilityMode(prev => ({ ...prev, dyslexiaMode: newMode }));
    },
  };

  if (!isEnabled) {
    return <div className={className} {...props}>{children}</div>;
  }

  return (
    <WellnessContext.Provider value={contextValue}>
      <div className={`wellness-container ${className}`} {...props}>
        {children}
        
        {/* Subtle scroll indicator (optional) */}
        {features.smoothScrollIndicator && (
          <div
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              height: '2px',
              backgroundColor: '#7C9885',
              zIndex: 9999,
              transition: 'width 0.2s ease',
              width: '0%',
              opacity: 0.3,
            }}
            className="wellness-scroll-indicator"
          />
        )}
        
        {/* Accessibility widget (zachowane) */}
        {features.accessibilityEnhancements && (
          <div
            style={{
              position: 'fixed',
              bottom: '20px',
              right: '20px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              padding: '16px',
              borderRadius: '12px',
              border: '1px solid rgba(124, 152, 133, 0.2)',
              fontSize: '14px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
              zIndex: 9998,
            }}
          >
            <div style={{ fontWeight: '600', marginBottom: '8px', color: '#7C9885' }}>
              Accessibility
            </div>
            <label style={{ display: 'block', marginBottom: '4px', fontSize: '12px' }}>
              <input
                type="checkbox"
                checked={accessibilityMode.dyslexiaMode}
                onChange={contextValue.toggleDyslexiaMode}
                style={{ marginRight: '8px' }}
              />
              Dyslexia Mode
            </label>
          </div>
        )}
      </div>
    </WellnessContext.Provider>
  );
};

export default WellnessProvider;