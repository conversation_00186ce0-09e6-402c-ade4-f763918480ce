import { Suspense } from 'react';
import { getAllPosts } from '@/data/blogPosts';
import BlogPageClientContent from './BlogPageClientContent';

export const metadata = {
  title: 'Blog - <PERSON><PERSON>, Podróże i Inspiracje z Bali',
  description: 'Najn<PERSON>ze artykuły o jodze na Bali, slow travel, medytacji i odkrywaniu piękna Wyspy Bogów. Porady, inspiracje i relacje z podróży.',
  keywords: ['blog joga bali', 'artykuły joga', 'slow travel bali', 'medytacja', 'inspiracje podróżnicze bali'],
  alternates: {
    canonical: '/blog',
  },
  openGraph: {
    title: 'Blog - Joga, Podróże i Inspiracje z Bali',
    description: 'Odkryj nasz blog o jodze i podróżach na Bali.',
    images: [
      {
        url: '/og-image-blog.jpg',
        width: 1200,
        height: 630,
        alt: 'Blog Bali Yoga Journey',
      },
    ],
  },
};

export default function BlogPage() {
  const posts = getAllPosts();

  if (!posts || !Array.isArray(posts)) {
    return <div>Brak postów do wyświetlenia.</div>;
  }

  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-[60vh]">Ładowanie artykułów...</div>}>
      <BlogPageClientContent posts={posts} />
    </Suspense>
  );
}