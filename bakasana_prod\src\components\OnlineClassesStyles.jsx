'use client';

import { useState, useEffect } from 'react';

export function useResponsiveStyles() {
  const [isDesktop, setIsDesktop] = useState(true);
  const [isTablet, setIsTablet] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsDesktop(width >= 1024);
      setIsTablet(width >= 768 && width < 1024);
      setIsMobile(width < 768);
    };

    setIsHydrated(true);
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // <PERSON><PERSON>ywamy domyślnych wartości desktop na pierwszym renderowaniu
  if (!isHydrated) {
    return { isDesktop: true, isTablet: false, isMobile: false };
  }

  return { isDesktop, isTablet, isMobile };
}

export const getHeroStyles = (isDesktop, isTablet, isMobile) => ({
  hero: {
    height: '100vh',
    minHeight: '600px',
    backgroundColor: '#FDFAF7',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    backgroundImage: `
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%)
    `
  },
  heroContent: {
    textAlign: 'center',
    transform: 'translateY(-30px)',
    padding: isMobile ? '0 20px' : '0'
  },
  heroTitle: {
    fontSize: isMobile ? '56px' : isTablet ? '72px' : '110px',
    fontFamily: 'Cormorant Garamond',
    fontWeight: 300,
    color: '#3D3A37',
    letterSpacing: '0.08em',
    marginBottom: '40px',
    lineHeight: '1.1'
  },
  heroSubtitle: {
    fontSize: '15px',
    fontFamily: 'Inter',
    fontWeight: 300,
    color: '#6B6560',
    letterSpacing: '0.15em',
    opacity: 0.8,
    marginBottom: 0
  },
  heroMeta: {
    position: 'absolute',
    bottom: '15%',
    left: '50%',
    transform: 'translateX(-50%)',
    textAlign: 'center',
    fontSize: '12px',
    fontFamily: 'Inter',
    fontWeight: 300,
    color: '#8B857F',
    letterSpacing: '0.2em',
    margin: 0
  }
});

export const getSectionStyles = (isDesktop, isTablet, isMobile) => ({
  section: {
    marginTop: isMobile ? '60px' : '100px',
    maxWidth: '1200px',
    margin: `${isMobile ? '60px' : '100px'} auto 0`,
    padding: isMobile ? '0 40px' : isTablet ? '0 60px' : '0 80px'
  },
  grid: {
    display: 'grid',
    gridTemplateColumns: isMobile ? '1fr' : '55% 45%',
    gap: isMobile ? '40px' : isTablet ? '60px' : '100px',
    alignItems: 'start'
  },
  gridReverse: {
    display: 'grid',
    gridTemplateColumns: isMobile ? '1fr' : '45% 55%',
    gap: isMobile ? '40px' : isTablet ? '60px' : '100px',
    alignItems: 'start'
  },
  sectionTitle: {
    fontSize: isMobile ? '32px' : isTablet ? '38px' : '48px', // 80% na tablet
    fontFamily: 'Cormorant Garamond',
    fontWeight: 300,
    color: '#3D3A37',
    letterSpacing: '0.02em',
    marginBottom: '36px',
    lineHeight: '1.2'
  },
  imageContainer: {
    height: isMobile ? '300px' : '580px',
    position: 'relative',
    aspectRatio: '3/4'
  },
  visualElement: {
    height: isMobile ? '300px' : '480px',
    backgroundColor: 'rgba(250, 248, 245, 0.3)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative'
  },
  ctaSection: {
    marginTop: isMobile ? '60px' : '100px',
    textAlign: 'center',
    maxWidth: '600px',
    margin: `${isMobile ? '60px' : '100px'} auto 0`,
    paddingBottom: isMobile ? '60px' : '100px',
    padding: isMobile ? '0 40px' : '0'
  },
  ctaTitle: {
    fontSize: isMobile ? '28px' : isTablet ? '34px' : '42px', // 80% na tablet
    fontFamily: 'Cormorant Garamond',
    fontWeight: 300,
    color: '#3D3A37',
    marginBottom: '24px',
    lineHeight: '1.3'
  },
  divider: {
    width: '40px',
    height: '1px',
    backgroundColor: '#C4A575',
    opacity: 0.25,
    margin: `${isMobile ? '60px' : '100px'} auto`
  }
});