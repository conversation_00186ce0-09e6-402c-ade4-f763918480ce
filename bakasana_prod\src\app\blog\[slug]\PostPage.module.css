.postContainer {
  max-width: 900px;
  margin: 4rem auto;
  padding: 3rem;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 0; /* <PERSON><PERSON><PERSON> */
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--color-bamboo-light);
  transition: all 0.4s ease;
}

.postContainer:hover {
  box-shadow: 0 12px 50px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.postHeader {
  margin-bottom: 3rem;
  border-bottom: 2px solid var(--color-temple);
  padding-bottom: 2rem;
  position: relative;
}

.postHeader::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-golden), var(--color-temple));
}

.postTitle {
  font-size: 3.2rem;
  font-weight: 800;
  color: var(--color-temple);
  margin-bottom: 1rem;
  font-family: var(--font-display);
  line-height: 1.1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.08);
  letter-spacing: -0.02em;
}

.postMeta {
  display: flex;
  align-items: center;
  gap: 2rem;
  font-size: 1rem;
  color: var(--color-wood-light);
  margin-bottom: 2rem;
  font-weight: 500;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(var(--color-temple), 0.05);
  border-radius: 0; /* Ostre kąty */
  border-left: 3px solid var(--color-temple);
}

.tagsContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-top: 1rem;
}

.tagBadge {
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--color-bamboo-light), var(--color-sage));
  color: var(--color-wood-dark);
  border: none;
  border-radius: 0; /* Ostre kąty */
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.tagBadge:hover {
  background: linear-gradient(135deg, var(--color-temple), var(--color-golden));
  color: white;
  transform: translateY(-1px);
}

.imageContainer {
  position: relative;
  width: 100%;
  margin-bottom: 3rem;
  border-radius: 0; /* Ostre kąty */
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.4s ease;
}

.imageContainer:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(-3px);
}

.postImage {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
  display: block;
  transition: transform 0.4s ease;
}

.imageContainer:hover .postImage {
  transform: scale(1.02);
}

.postContent {
  line-height: 1.9;
  font-size: 1.15rem;
  color: var(--color-wood-dark);
  font-weight: 400;
}

/* Enhanced markdown content styles */
.postContent h1,
.postContent h2,
.postContent h3,
.postContent h4,
.postContent h5,
.postContent h6 {
  color: var(--color-temple);
  margin-top: 2.5em;
  margin-bottom: 1.2em;
  font-family: var(--font-display);
  font-weight: 700;
  position: relative;
}

.postContent h2::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--color-temple), var(--color-golden));
  border-radius: 0;
}

.postContent h1 { font-size: 2.6rem; }
.postContent h2 { font-size: 2.2rem; }
.postContent h3 { font-size: 1.8rem; }
.postContent h4 { font-size: 1.5rem; font-weight: 600; }

.postContent p {
  margin-bottom: 1.8em;
  text-align: justify;
}

.postContent a {
  color: var(--color-temple);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 600;
  border-bottom: 2px solid transparent;
}

.postContent a:hover {
  color: var(--color-golden);
  border-bottom-color: var(--color-golden);
  transform: translateY(-1px);
}

.postContent ul,
.postContent ol {
  margin-left: 2rem;
  margin-bottom: 2em;
}

.postContent li {
  margin-bottom: 0.8em;
  position: relative;
  padding-left: 0.5rem;
}

.postContent ul li::before {
  content: '▸';
  color: var(--color-temple);
  font-weight: bold;
  position: absolute;
  left: -1rem;
}

.postContent blockquote {
  border-left: 6px solid var(--color-temple);
  padding: 1.5rem 2rem;
  margin: 2.5em 0;
  background: rgba(var(--color-temple), 0.03);
  color: var(--color-wood-light);
  font-style: italic;
  font-size: 1.1em;
  border-radius: 0; /* Ostre kąty */
  position: relative;
}

.postContent blockquote::before {
  content: '"';
  font-size: 4rem;
  color: var(--color-temple);
  opacity: 0.3;
  position: absolute;
  top: -10px;
  left: 20px;
  font-family: serif;
}

.postContent code {
  background-color: var(--color-rice-dark);
  padding: 0.3em 0.6em;
  border-radius: 0; /* Ostre kąty */
  font-size: 0.9em;
  font-family: var(--font-mono);
  border: 1px solid var(--color-bamboo-light);
}

.postContent pre {
  background-color: var(--color-rice-dark);
  padding: 1.5rem;
  border-radius: 0; /* Ostre kąty */
  overflow-x: auto;
  margin-bottom: 2em;
  border: 1px solid var(--color-bamboo-light);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.backLink {
  display: inline-flex;
  align-items: center;
  margin-top: 4rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--color-temple), var(--color-golden));
  color: white;
  text-decoration: none;
  font-weight: 600;
  border-radius: 0; /* Ostre kąty */
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(var(--color-temple), 0.3);
}

.backLink:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(var(--color-temple), 0.4);
  background: linear-gradient(135deg, var(--color-golden), var(--color-temple));
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .postContainer {
    margin: 3rem auto;
    padding: 1.5rem;
  }
  .postTitle {
    font-size: 2.2rem;
  }
  .postContent {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .postContainer {
    margin: 2rem 1rem;
    padding: 1rem;
  }
  .postTitle {
    font-size: 1.8rem;
  }
  .postMeta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
} 