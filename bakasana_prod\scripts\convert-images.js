const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');

const imagesToConvert = [
  {
    input: 'public/images/blog/yoga-beginner.jpg',
    output: 'public/images/blog/yoga-beginner.webp'
  },
  {
    input: 'public/images/blog/meditation-bali.jpg',
    output: 'public/images/blog/meditation-bali.webp'
  },
  {
    input: 'public/images/gallery/IMG_5914.jpg',
    output: 'public/images/gallery/IMG_5914.webp'
  },
  {
    input: 'public/images/gallery/IMG_6624.jpg',
    output: 'public/images/gallery/IMG_6624.webp'
  }
];

async function convertImages() {
  for (const image of imagesToConvert) {
    try {
      console.log(`Konwertuję ${image.input} do ${image.output}...`);
      
      await sharp(image.input)
        .webp({
          quality: 80,
          effort: 6
        })
        .toFile(image.output);
      
      console.log(`✓ Zakończono konwersję ${image.output}`);
    } catch (error) {
      console.error(`✗ Błąd podczas konwersji ${image.input}:`, error);
    }
  }
}

convertImages().catch(console.error); 