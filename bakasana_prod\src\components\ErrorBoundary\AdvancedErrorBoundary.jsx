'use client';

import React from 'react';
import { motion } from 'framer-motion';

class AdvancedErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console for development
    console.error('Error caught by boundary:', error, errorInfo);

    // Track error in analytics
    if (typeof window !== 'undefined') {
      // Sentry
      if (window.Sentry) {
        window.Sentry.captureException(error, {
          contexts: {
            react: {
              componentStack: errorInfo.componentStack,
            },
          },
          tags: {
            component: 'ErrorBoundary',
            page: window.location.pathname,
          },
        });
      }

      // Mixpanel
      if (window.mixpanel) {
        window.mixpanel.track('JavaScript Error', {
          error_message: error.message,
          error_stack: error.stack,
          component_stack: errorInfo.componentStack,
          page: window.location.pathname,
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
        });
      }

      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'exception', {
          description: error.message,
          fatal: true,
          event_category: 'JavaScript Error',
          event_label: window.location.pathname,
        });
      }
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-sanctuary flex items-center justify-center px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-md w-full text-center"
          >
            {/* Om Symbol */}
            <motion.div
              initial={{ scale: 0.8, rotate: -10 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-6xl text-golden-lotus mb-8"
            >
              ॐ
            </motion.div>

            {/* Error Message */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h1 className="text-3xl font-primary text-charcoal mb-4">
                Ups... Coś poszło nie tak
              </h1>
              
              <p className="text-lg text-charcoal/70 mb-8 leading-relaxed">
                Przepraszamy, ale wystąpił nieoczekiwany błąd. 
                Nasz zespół został powiadomiony i pracuje nad rozwiązaniem.
              </p>

              {/* Actions */}
              <div className="space-y-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => window.location.reload()}
                  className="btn-primary w-full"
                >
                  Odśwież stronę
                </motion.button>
                
                <motion.a
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  href="/"
                  className="btn-secondary w-full block"
                >
                  Wróć na stronę główną
                </motion.a>
                
                <motion.a
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  href="/kontakt"
                  className="btn-ghost w-full block"
                >
                  Skontaktuj się z nami
                </motion.a>
              </div>

              {/* Debug Info (only in development) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <motion.details
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  className="mt-8 text-left bg-warm-sanctuary p-4 rectangular"
                >
                  <summary className="cursor-pointer text-sm font-medium text-charcoal/60 mb-2">
                    Szczegóły błędu (tylko w trybie deweloperskim)
                  </summary>
                  <pre className="text-xs text-charcoal/50 overflow-auto">
                    {this.state.error.toString()}
                    {this.state.errorInfo.componentStack}
                  </pre>
                </motion.details>
              )}
            </motion.div>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AdvancedErrorBoundary;