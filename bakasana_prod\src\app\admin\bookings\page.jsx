'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminBookingsPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const router = useRouter();

  useEffect(() => {
    // Sprawdź autentykację
    const token = localStorage.getItem('admin-token');
    if (!token) {
      router.push('/admin');
      return;
    }

    verifyTokenAndLoadData(token);
  }, [router]);

  const verifyTokenAndLoadData = async (token) => {
    try {
      const response = await fetch('/api/admin/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setIsAuthenticated(true);
        await loadBookings();
      } else {
        localStorage.removeItem('admin-token');
        router.push('/admin');
      }
    } catch (error) {
      console.error('Auth verification failed:', error);
      router.push('/admin');
    }
    setLoading(false);
  };

  const loadBookings = async () => {
    try {
      const response = await fetch('/api/admin/bookings');
      if (response.ok) {
        const data = await response.json();
        setBookings(data.bookings || []);
      }
    } catch (error) {
      console.error('Failed to load bookings:', error);
    }
  };

  const updateBookingStatus = async (bookingId, newStatus) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (response.ok) {
        await loadBookings(); // Odśwież listę
      }
    } catch (error) {
      console.error('Failed to update booking:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'Oczekuje';
      case 'confirmed': return 'Potwierdzona';
      case 'cancelled': return 'Anulowana';
      default: return 'Nieznany';
    }
  };

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    return booking.status === filter;
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"></div>
          <p className="text-temple">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rice to-mist">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-temple/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/admin')}
                className="text-temple hover:text-temple/70 mr-4"
              >
                ← Powrót
              </button>
              <h1 className="text-xl font-serif text-temple">Zarządzanie Rezerwacjami</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-wood-light">
                {filteredBookings.length} rezerwacji
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        
        {/* Filters */}
        <div className="bg-white rounded-xl shadow-soft p-6 mb-6">
          <h3 className="text-lg font-medium text-temple mb-4">Filtry</h3>
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'Wszystkie', count: bookings.length },
              { key: 'pending', label: 'Oczekujące', count: bookings.filter(b => b.status === 'pending').length },
              { key: 'confirmed', label: 'Potwierdzone', count: bookings.filter(b => b.status === 'confirmed').length },
              { key: 'cancelled', label: 'Anulowane', count: bookings.filter(b => b.status === 'cancelled').length }
            ].map(filterOption => (
              <button
                key={filterOption.key}
                onClick={() => setFilter(filterOption.key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filter === filterOption.key
                    ? 'bg-temple text-white'
                    : 'bg-temple/10 text-temple hover:bg-temple/20'
                }`}
              >
                {filterOption.label} ({filterOption.count})
              </button>
            ))}
          </div>
        </div>

        {/* Bookings List */}
        <div className="bg-white rounded-xl shadow-soft overflow-hidden">
          {filteredBookings.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-6xl mb-4">📅</div>
              <h3 className="text-lg font-medium text-temple mb-2">Brak rezerwacji</h3>
              <p className="text-wood-light">
                {filter === 'all' 
                  ? 'Nie ma jeszcze żadnych rezerwacji.'
                  : `Nie ma rezerwacji o statusie "${getStatusText(filter)}".`
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-temple/10">
                <thead className="bg-temple/5">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider">
                      Klient
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider">
                      Program
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider">
                      Akcje
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-temple/10">
                  {filteredBookings.map((booking) => (
                    <tr key={booking.id} className="hover:bg-temple/5">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-temple">
                            {booking.firstName} {booking.lastName}
                          </div>
                          <div className="text-sm text-wood-light">{booking.email}</div>
                          <div className="text-sm text-wood-light">{booking.phone}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-temple">{booking.program}</div>
                        <div className="text-sm text-wood-light">{booking.destination}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-temple">
                          {new Date(booking.createdAt).toLocaleDateString('pl-PL')}
                        </div>
                        <div className="text-sm text-wood-light">
                          {new Date(booking.createdAt).toLocaleTimeString('pl-PL')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                          {getStatusText(booking.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {booking.status === 'pending' && (
                            <>
                              <button
                                onClick={() => updateBookingStatus(booking.id, 'confirmed')}
                                className="text-green-600 hover:text-green-900"
                              >
                                Potwierdź
                              </button>
                              <button
                                onClick={() => updateBookingStatus(booking.id, 'cancelled')}
                                className="text-red-600 hover:text-red-900"
                              >
                                Anuluj
                              </button>
                            </>
                          )}
                          {booking.status === 'confirmed' && (
                            <button
                              onClick={() => updateBookingStatus(booking.id, 'cancelled')}
                              className="text-red-600 hover:text-red-900"
                            >
                              Anuluj
                            </button>
                          )}
                          {booking.status === 'cancelled' && (
                            <button
                              onClick={() => updateBookingStatus(booking.id, 'pending')}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Przywróć
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
