'use client';

import React, { memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import SEOBreadcrumbs from '@/components/SEO/SEOBreadcrumbs';

const OptimizedBreadcrumbs = memo(() => {
  const pathname = usePathname();
  
  // Don't show breadcrumbs on homepage
  if (pathname === '/') return null;
  


  return (
    <div className="bg-sanctuary py-3 border-b border-whisper">
      <div className="container mx-auto px-4">
        <SEOBreadcrumbs
          className="breadcrumb-navigation"
          showHome={true}
          separator="/"
          maxItems={5}
        />
      </div>
    </div>
  );
});

function getPageName(segment) {
  const pageNames = {
    'program': 'Program',
    'retreaty': 'Retreaty',
    'galeria': 'Galeria',
    'kontakt': 'Kontakt',
    'blog': 'Blog',
    'o-mnie': 'O mnie',
    'zajecia-online': 'Zajęcia Online',
    'rezerwacja': 'Rezerwa<PERSON><PERSON>',
    'mapa': 'Mapa',
    'wellness': 'Wellness',
    'polityka-prywatnosci': 'Polityka Prywatności'
  };
  
  return pageNames[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}

OptimizedBreadcrumbs.displayName = 'OptimizedBreadcrumbs';

export default OptimizedBreadcrumbs;