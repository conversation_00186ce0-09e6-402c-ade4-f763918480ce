# 🔄 BAKASANA DESIGN SYSTEM MIGRATION GUIDE

## Overview

This guide helps you migrate existing components to use the new unified Bakasana Design System. The migration ensures visual consistency, improved maintainability, and better accessibility across the entire website.

---

## 🎯 Migration Priorities

### ✅ Completed (High Priority)
- [x] **Color System Consolidation** - Single source of truth in `design-tokens.css`
- [x] **Button Components** - Unified styling patterns and hover states
- [x] **Form Elements** - Consistent input, textarea, and validation styles
- [x] **Typography System** - Standardized font weights, sizes, and spacing
- [x] **Icon System** - Unified icon component with consistent sizing
- [x] **Navigation Components** - Updated PerfectNavbar and MinimalistNavbar
- [x] **Card Components** - Consistent styling and hover effects
- [x] **Animation System** - Unified durations and easing functions

### 🔄 In Progress (Medium Priority)
- [ ] **Hero Sections** - Update to use unified typography and spacing
- [ ] **Footer Components** - Standardize styling and layout
- [ ] **Modal/Dialog Components** - Consistent overlay and content styling
- [ ] **Loading States** - Unified skeleton and spinner components

### 📋 Pending (Low Priority)
- [ ] **Page-specific Components** - Retreat cards, testimonials, etc.
- [ ] **Legacy CSS Cleanup** - Remove duplicate styles and unused classes
- [ ] **Performance Optimization** - Optimize CSS bundle size
- [ ] **Documentation Updates** - Update component documentation

---

## 🔧 Migration Steps

### Step 1: Update Color References

#### Before (Multiple Systems)
```css
/* Old inconsistent color usage */
.component {
  background: #FDFCF8;
  color: #2A2724;
  border: 1px solid rgba(139, 115, 85, 0.2);
}

.component:hover {
  background: #B8935C;
  color: white;
}
```

#### After (Unified System)
```css
/* New unified color usage */
.component {
  background: var(--sanctuary);
  color: var(--charcoal);
  border: 1px solid var(--border-light);
}

.component:hover {
  background: var(--enterprise-brown);
  color: var(--sanctuary);
}
```

#### Tailwind Classes
```jsx
// Before
<div className="bg-[#FDFCF8] text-[#2A2724] border border-[rgba(139,115,85,0.2)]">

// After
<div className="bg-sanctuary text-charcoal border border-stone-light">
```

### Step 2: Update Typography

#### Before (Inconsistent Typography)
```jsx
// Old inconsistent typography
<h1 className="font-cormorant text-8xl font-light tracking-[0.3em]">
  Title
</h1>
<p className="font-inter text-base leading-[1.8]">
  Body text
</p>
```

#### After (Unified Typography)
```jsx
// New unified typography
<h1 className="font-primary text-hero font-light tracking-ultra">
  Title
</h1>
<p className="font-secondary text-body leading-relaxed">
  Body text
</p>
```

#### Using Typography Components
```jsx
import { HeroTitle, SectionTitle, CardTitle } from '@/components/ui/UnifiedTypography';

<HeroTitle>BAKASANA</HeroTitle>
<SectionTitle>Our Retreats</SectionTitle>
<CardTitle>Mindful Practice</CardTitle>
```

### Step 3: Update Button Components

#### Before (Inconsistent Buttons)
```jsx
// Old button with inline styles
<button 
  className="bg-transparent border border-charcoal text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4"
  style={{ transition: '300ms' }}
>
  Click Me
</button>
```

#### After (Unified Button)
```jsx
import { UnifiedButton } from '@/components/ui/UnifiedButton';

<UnifiedButton variant="secondary" size="lg">
  Click Me
</UnifiedButton>
```

### Step 4: Update Form Elements

#### Before (Inconsistent Forms)
```jsx
// Old form input
<input 
  className="bg-sanctuary border border-stone-light/30 text-charcoal focus:border-enterprise-brown focus:ring-2 focus:ring-enterprise-brown/10"
  style={{ transition: 'all 300ms' }}
/>
```

#### After (Unified Input)
```jsx
import { UnifiedInput } from '@/components/ui/UnifiedInput';

<UnifiedInput 
  variant="default" 
  size="md" 
  placeholder="Enter text"
/>
```

### Step 5: Update Icon Usage

#### Before (Multiple Icon Libraries)
```jsx
// Old inconsistent icon usage
import { Menu } from 'lucide-react';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

<Menu className="w-6 h-6 text-enterprise-brown" />
<ChevronDownIcon className="w-5 h-5 text-temple-gold" />
```

#### After (Unified Icon System)
```jsx
import Icon from '@/components/ui/Icon';

<Icon name="menu" size="lg" color="primary" />
<Icon name="chevron-down" size="md" color="secondary" />
```

### Step 6: Update Card Components

#### Before (Inconsistent Cards)
```jsx
// Old card with mixed styling
<div className="bg-sanctuary border border-stone-light/20 hover:shadow-elegant hover:border-enterprise-brown/20 hover:-translate-y-1 transition-all duration-300">
  Content
</div>
```

#### After (Unified Card)
```jsx
import { UnifiedCard } from '@/components/ui/UnifiedCard';

<UnifiedCard variant="default" padding="lg">
  Content
</UnifiedCard>
```

---

## 🎨 Component-Specific Migrations

### Navigation Components

#### PerfectNavbar Updates
- ✅ Updated background colors to use `bg-sanctuary/85`
- ✅ Standardized transition durations to `duration-slow`
- ✅ Updated logo styling to use unified gradient
- ✅ Consistent hover states using `hover:text-temple-gold`

#### MinimalistNavbar Updates
- ✅ Updated background to use `bg-sanctuary/85`
- ✅ Standardized font classes to use `font-primary`
- ✅ Updated transition timing to use `duration-normal`

### Button Components

#### UnifiedButton Updates
- ✅ Consolidated button variants (primary, secondary, ghost, minimal)
- ✅ Standardized hover effects using design tokens
- ✅ Updated focus states for accessibility
- ✅ Consistent sizing system (sm, md, lg, xl)

#### Legacy Button Updates
- ✅ Updated to use unified color system
- ✅ Standardized hover behavior (no more opacity changes)
- ✅ Consistent transition timing

### Form Components

#### UnifiedInput Updates
- ✅ Standardized focus states using `focus:shadow-focus`
- ✅ Consistent border colors and hover effects
- ✅ Updated placeholder styling
- ✅ Improved accessibility with proper focus indicators

### Typography Components

#### UnifiedTypography Updates
- ✅ Updated HeroTitle to use `text-hero` and `tracking-ultra`
- ✅ Standardized SectionTitle with `text-display-xl`
- ✅ Updated CardTitle to use `text-heading-lg`
- ✅ Consistent font family usage (`font-primary` vs `font-secondary`)

---

## 🔍 Testing Checklist

### Visual Consistency
- [ ] All buttons use consistent styling and hover effects
- [ ] Form elements have uniform focus states
- [ ] Typography follows the established hierarchy
- [ ] Colors match the design system exactly
- [ ] Icons are consistently sized and colored

### Accessibility
- [ ] All color combinations meet WCAG AA standards
- [ ] Focus states are clearly visible
- [ ] Interactive elements have proper hover feedback
- [ ] Text has sufficient contrast ratios

### Responsive Design
- [ ] Components work across all breakpoints
- [ ] Typography scales appropriately
- [ ] Spacing is consistent on mobile and desktop
- [ ] Touch targets are appropriately sized

### Performance
- [ ] No duplicate CSS rules
- [ ] Efficient use of CSS custom properties
- [ ] Minimal bundle size impact
- [ ] Fast loading times maintained

---

## 🚨 Common Issues & Solutions

### Issue: Colors Don't Match
**Problem**: Components still using old hardcoded colors
**Solution**: Replace all hardcoded hex values with CSS custom properties

```css
/* Wrong */
background: #FDFCF8;

/* Correct */
background: var(--sanctuary);
```

### Issue: Inconsistent Hover Effects
**Problem**: Some buttons use opacity, others use color changes
**Solution**: Use unified hover patterns from design system

```css
/* Wrong */
.button:hover { opacity: 0.7; }

/* Correct */
.button:hover { 
  background: var(--temple-gold);
  color: var(--sanctuary);
}
```

### Issue: Typography Not Scaling
**Problem**: Fixed font sizes don't work on mobile
**Solution**: Use design token typography scale

```css
/* Wrong */
font-size: 48px;

/* Correct */
font-size: var(--text-display-xl);
```

### Issue: Icons Different Sizes
**Problem**: Icons using different sizing systems
**Solution**: Use unified Icon component

```jsx
// Wrong
<MenuIcon className="w-6 h-6" />

// Correct
<Icon name="menu" size="lg" />
```

---

## 📋 Migration Checklist

### For Each Component:
- [ ] Replace hardcoded colors with design tokens
- [ ] Update typography to use unified scale
- [ ] Standardize spacing using design system
- [ ] Update animations to use unified durations
- [ ] Replace icons with unified Icon component
- [ ] Test accessibility and responsiveness
- [ ] Update component documentation

### For Each Page:
- [ ] Audit all components for consistency
- [ ] Test visual hierarchy
- [ ] Verify responsive behavior
- [ ] Check accessibility compliance
- [ ] Validate performance impact

---

## 🎯 Next Steps

1. **Complete Medium Priority Items**: Focus on hero sections and footer components
2. **Legacy CSS Cleanup**: Remove unused styles and consolidate duplicates
3. **Performance Optimization**: Analyze and optimize CSS bundle size
4. **Documentation**: Update all component documentation with new patterns
5. **Training**: Ensure team understands new design system patterns

---

## 📞 Support

If you encounter issues during migration:
1. Check the [Design System Documentation](./DESIGN_SYSTEM.md)
2. Review existing unified components in `src/components/ui/`
3. Test changes across different screen sizes
4. Validate accessibility with screen readers

---

*Migration Guide Version: 1.0.0*
*Last Updated: 2025-07-21*
