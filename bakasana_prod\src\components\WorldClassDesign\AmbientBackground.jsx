'use client';

import React, { useEffect, useState } from 'react';

/**
 * 🌟 AMBIENT MODE - TOP 1% DESIGN FEATURE
 * Tło zmienia odcień w zależności od pory dnia
 * Rano: cieplejsze tony, Wieczorem: chłodniejsze
 * Subtelne (2-3% różnicy) - ultra premium
 */
const AmbientBackground = ({ children, className = '' }) => {
  const [timeOfDay, setTimeOfDay] = useState('day');
  const [ambientColor, setAmbientColor] = useState('');

  useEffect(() => {
    const updateAmbientColor = () => {
      const now = new Date();
      const hours = now.getHours();
      
      let newTimeOfDay;
      let newAmbientColor;
      
      if (hours >= 5 && hours < 12) {
        // Rano: cieplejsze tony
        newTimeOfDay = 'morning';
        newAmbientColor = 'rgba(255, 248, 220, 0.015)'; // Sehr subtle warm
      } else if (hours >= 12 && hours < 18) {
        // Dzień: neutralne
        newTimeOfDay = 'day';
        newAmbientColor = 'rgba(255, 255, 255, 0.01)'; // Prawie niewidoczne
      } else if (hours >= 18 && hours < 22) {
        // Wieczór: chłodniejsze
        newTimeOfDay = 'evening';
        newAmbientColor = 'rgba(245, 248, 255, 0.015)'; // Sehr subtle cool
      } else {
        // Noc: bardzo chłodne
        newTimeOfDay = 'night';
        newAmbientColor = 'rgba(240, 245, 255, 0.02)'; // Minimal cool
      }
      
      setTimeOfDay(newTimeOfDay);
      setAmbientColor(newAmbientColor);
    };

    updateAmbientColor();
    const interval = setInterval(updateAmbientColor, 60000); // Update co minutę

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Noise texture dla invisible luxury */}
      <div 
        className="absolute inset-0 pointer-events-none"
        style={{
          background: `${ambientColor}`,
          transition: 'background-color 30s ease-in-out', // Bardzo powolna zmiana
          zIndex: 1,
        }}
      />
      
      {/* Subtle noise pattern */}
      <div 
        className="absolute inset-0 pointer-events-none opacity-[0.015]"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: '256px 256px',
          zIndex: 2,
        }}
      />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default React.memo(AmbientBackground);