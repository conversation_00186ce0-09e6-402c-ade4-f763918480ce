'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAccessibility } from './AccessibilityProvider';

/**
 * ♿ ACCESSIBLE FORM COMPONENTS
 * 
 * WCAG 2.1 AA compliant form components with:
 * - Proper labeling and associations
 * - Error handling and announcements
 * - Keyboard navigation support
 * - Screen reader optimizations
 * - Validation feedback
 * - Required field indicators
 */

// Accessible Input Component
export const AccessibleInput = ({
  id,
  label,
  type = 'text',
  required = false,
  error = '',
  helpText = '',
  value,
  onChange,
  onBlur,
  placeholder = '',
  autoComplete = '',
  className = '',
  ...props
}) => {
  const { announce } = useAccessibility();
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = `${inputId}-error`;
  const helpId = `${inputId}-help`;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    onChange?.(e);
    
    // Announce validation errors to screen readers
    if (error && e.target.value) {
      announce(`Błąd walidacji: ${error}`, 'assertive');
    }
  };

  return (
    <div className={`form-field ${className}`}>
      <label 
        htmlFor={inputId}
        className={`form-label ${required ? 'required' : ''}`}
      >
        {label}
        {required && (
          <span className="required-indicator" aria-label="pole wymagane">
            *
          </span>
        )}
      </label>
      
      {helpText && (
        <div id={helpId} className="form-help-text">
          {helpText}
        </div>
      )}
      
      <input
        ref={inputRef}
        id={inputId}
        type={type}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        autoComplete={autoComplete}
        required={required}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`${helpText ? helpId : ''} ${error ? errorId : ''}`.trim()}
        className={`form-input ${error ? 'error' : ''} ${isFocused ? 'focused' : ''}`}
        {...props}
      />
      
      {error && (
        <div 
          id={errorId}
          className="form-error"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  );
};

// Accessible Textarea Component
export const AccessibleTextarea = ({
  id,
  label,
  required = false,
  error = '',
  helpText = '',
  value,
  onChange,
  onBlur,
  placeholder = '',
  rows = 4,
  className = '',
  ...props
}) => {
  const { announce } = useAccessibility();
  const [isFocused, setIsFocused] = useState(false);
  const textareaRef = useRef(null);

  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = `${textareaId}-error`;
  const helpId = `${textareaId}-help`;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    onChange?.(e);
    
    if (error && e.target.value) {
      announce(`Błąd walidacji: ${error}`, 'assertive');
    }
  };

  return (
    <div className={`form-field ${className}`}>
      <label 
        htmlFor={textareaId}
        className={`form-label ${required ? 'required' : ''}`}
      >
        {label}
        {required && (
          <span className="required-indicator" aria-label="pole wymagane">
            *
          </span>
        )}
      </label>
      
      {helpText && (
        <div id={helpId} className="form-help-text">
          {helpText}
        </div>
      )}
      
      <textarea
        ref={textareaRef}
        id={textareaId}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        required={required}
        rows={rows}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`${helpText ? helpId : ''} ${error ? errorId : ''}`.trim()}
        className={`form-textarea ${error ? 'error' : ''} ${isFocused ? 'focused' : ''}`}
        {...props}
      />
      
      {error && (
        <div 
          id={errorId}
          className="form-error"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  );
};

// Accessible Select Component
export const AccessibleSelect = ({
  id,
  label,
  options = [],
  required = false,
  error = '',
  helpText = '',
  value,
  onChange,
  onBlur,
  placeholder = 'Wybierz opcję',
  className = '',
  ...props
}) => {
  const { announce } = useAccessibility();
  const [isFocused, setIsFocused] = useState(false);
  const selectRef = useRef(null);

  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = `${selectId}-error`;
  const helpId = `${selectId}-help`;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const handleChange = (e) => {
    onChange?.(e);
    
    const selectedOption = options.find(opt => opt.value === e.target.value);
    if (selectedOption) {
      announce(`Wybrano: ${selectedOption.label}`);
    }
  };

  return (
    <div className={`form-field ${className}`}>
      <label 
        htmlFor={selectId}
        className={`form-label ${required ? 'required' : ''}`}
      >
        {label}
        {required && (
          <span className="required-indicator" aria-label="pole wymagane">
            *
          </span>
        )}
      </label>
      
      {helpText && (
        <div id={helpId} className="form-help-text">
          {helpText}
        </div>
      )}
      
      <select
        ref={selectRef}
        id={selectId}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        required={required}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`${helpText ? helpId : ''} ${error ? errorId : ''}`.trim()}
        className={`form-select ${error ? 'error' : ''} ${isFocused ? 'focused' : ''}`}
        {...props}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <div 
          id={errorId}
          className="form-error"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  );
};

// Accessible Checkbox Component
export const AccessibleCheckbox = ({
  id,
  label,
  checked = false,
  onChange,
  required = false,
  error = '',
  className = '',
  ...props
}) => {
  const { announce } = useAccessibility();
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = `${checkboxId}-error`;

  const handleChange = (e) => {
    onChange?.(e);
    announce(`${label} ${e.target.checked ? 'zaznaczone' : 'odznaczone'}`);
  };

  return (
    <div className={`form-field checkbox-field ${className}`}>
      <div className="checkbox-wrapper">
        <input
          id={checkboxId}
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          required={required}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? errorId : undefined}
          className={`form-checkbox ${error ? 'error' : ''}`}
          {...props}
        />
        <label 
          htmlFor={checkboxId}
          className={`checkbox-label ${required ? 'required' : ''}`}
        >
          {label}
          {required && (
            <span className="required-indicator" aria-label="pole wymagane">
              *
            </span>
          )}
        </label>
      </div>
      
      {error && (
        <div 
          id={errorId}
          className="form-error"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  );
};

export default {
  AccessibleInput,
  AccessibleTextarea,
  AccessibleSelect,
  AccessibleCheckbox
};
