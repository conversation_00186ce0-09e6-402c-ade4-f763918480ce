"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-vitals";
exports.ids = ["vendor-chunks/web-vitals"];
exports.modules = {

/***/ "(ssr)/./node_modules/web-vitals/dist/web-vitals.js":
/*!****************************************************!*\
  !*** ./node_modules/web-vitals/dist/web-vitals.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLSThresholds: () => (/* binding */ b),\n/* harmony export */   FCPThresholds: () => (/* binding */ L),\n/* harmony export */   FIDThresholds: () => (/* binding */ D),\n/* harmony export */   INPThresholds: () => (/* binding */ j),\n/* harmony export */   LCPThresholds: () => (/* binding */ U),\n/* harmony export */   TTFBThresholds: () => (/* binding */ X),\n/* harmony export */   getCLS: () => (/* binding */ S),\n/* harmony export */   getFCP: () => (/* binding */ w),\n/* harmony export */   getFID: () => (/* binding */ x),\n/* harmony export */   getINP: () => (/* binding */ Q),\n/* harmony export */   getLCP: () => (/* binding */ W),\n/* harmony export */   getTTFB: () => (/* binding */ Z),\n/* harmony export */   onCLS: () => (/* binding */ S),\n/* harmony export */   onFCP: () => (/* binding */ w),\n/* harmony export */   onFID: () => (/* binding */ x),\n/* harmony export */   onINP: () => (/* binding */ Q),\n/* harmony export */   onLCP: () => (/* binding */ W),\n/* harmony export */   onTTFB: () => (/* binding */ Z)\n/* harmony export */ });\nvar e,n,t,i,r,a=-1,o=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(a=n.timeStamp,e(n))}),!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0]},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),i=\"navigate\";a>=0?i=\"back-forward-cache\":t&&(document.prerendering||u()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":t.type&&(i=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v3-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},d=function(e,n,t,i){var r,a;return function(o){n.value>=0&&(o||i)&&((a=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=a,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){var n=function(n){\"pagehide\"!==n.type&&\"hidden\"!==document.visibilityState||e(n)};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},v=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),o((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},L=[1800,3e3],w=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"FCP\"),a=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(a.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-u(),0),r.entries.push(e),t(!0)))}))}));a&&(t=d(e,r,L,n.reportAllChanges),o((function(i){r=f(\"FCP\"),t=d(e,r,L,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,t(!0)}))})))}))},b=[.1,.25],S=function(e,n){n=n||{},w(v((function(){var t,i=f(\"CLS\",0),r=0,a=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=a[0],t=a[a.length-1];r&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(r+=e.value,a.push(e)):(r=e.value,a=[e])}})),r>i.value&&(i.value=r,i.entries=a,t())},u=s(\"layout-shift\",c);u&&(t=d(e,i,b,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),o((function(){r=0,i=f(\"CLS\",0),t=d(e,i,b,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A={passive:!0,capture:!0},I=new Date,P=function(i,r){e||(e=r,n=i,t=new Date,k(removeEventListener),F())},F=function(){if(n>=0&&n<t-I){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+n};i.forEach((function(e){e(r)})),i=[]}},M=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){P(e,n),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",t,A),removeEventListener(\"pointercancel\",i,A)};addEventListener(\"pointerup\",t,A),addEventListener(\"pointercancel\",i,A)}(n,e):P(n,e)}},k=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,M,A)}))},D=[100,300],x=function(t,r){r=r||{},C((function(){var a,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),a(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);a=d(t,u,D,r.reportAllChanges),h&&p(v((function(){m(h.takeRecords()),h.disconnect()}))),h&&o((function(){var o;u=f(\"FID\"),a=d(t,u,D,r.reportAllChanges),i=[],n=-1,e=null,k(addEventListener),o=l,i.push(o),F()}))}))},B=0,R=1/0,H=0,N=function(e){e.forEach((function(e){e.interactionId&&(R=Math.min(R,e.interactionId),H=Math.max(H,e.interactionId),B=H?(H-R)/7+1:0)}))},O=function(){return r?B:performance.interactionCount||0},q=function(){\"interactionCount\"in performance||r||(r=s(\"event\",N,{type:\"event\",buffered:!0,durationThreshold:0}))},j=[200,500],_=0,z=function(){return O()-_},G=[],J={},K=function(e){var n=G[G.length-1],t=J[e.interactionId];if(t||G.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};J[i.id]=i,G.push(i)}G.sort((function(e,n){return n.latency-e.latency})),G.splice(10).forEach((function(e){delete J[e.id]}))}},Q=function(e,n){n=n||{},C((function(){var t;q();var i,r=f(\"INP\"),a=function(e){e.forEach((function(e){(e.interactionId&&K(e),\"first-input\"===e.entryType)&&(!G.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&K(e))}));var n,t=(n=Math.min(G.length-1,Math.floor(z()/50)),G[n]);t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())},c=s(\"event\",a,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});i=d(e,r,j,n.reportAllChanges),c&&(\"PerformanceEventTiming\"in window&&\"interactionId\"in PerformanceEventTiming.prototype&&c.observe({type:\"first-input\",buffered:!0}),p((function(){a(c.takeRecords()),r.value<0&&z()>0&&(r.value=0,r.entries=[]),i(!0)})),o((function(){G=[],_=O(),r=f(\"INP\"),i=d(e,r,j,n.reportAllChanges)})))}))},U=[2500,4e3],V={},W=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"LCP\"),a=function(e){var n=e[e.length-1];n&&n.startTime<i.firstHiddenTime&&(r.value=Math.max(n.startTime-u(),0),r.entries=[n],t())},c=s(\"largest-contentful-paint\",a);if(c){t=d(e,r,U,n.reportAllChanges);var m=v((function(){V[r.id]||(a(c.takeRecords()),c.disconnect(),V[r.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return setTimeout(m,0)}),!0)})),p(m),o((function(i){r=f(\"LCP\"),t=d(e,r,U,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,V[r.id]=!0,t(!0)}))}))}}))},X=[800,1800],Y=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Z=function(e,n){n=n||{};var t=f(\"TTFB\"),i=d(e,t,X,n.reportAllChanges);Y((function(){var r=c();if(r){var a=r.responseStart;if(a<=0||a>performance.now())return;t.value=Math.max(a-u(),0),t.entries=[r],i(!0),o((function(){t=f(\"TTFB\",0),(i=d(e,t,X,n.reportAllChanges))(!0)}))}}))};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2ViLXZpdGFscy9kaXN0L3dlYi12aXRhbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsaUNBQWlDLHlDQUF5QyxrQ0FBa0MsTUFBTSxjQUFjLHVHQUF1RyxjQUFjLFVBQVUsK0JBQStCLGlCQUFpQix1QkFBdUIsbUpBQW1KLE9BQU8scUtBQXFLLG1CQUFtQixJQUFJLHdEQUF3RCwyQ0FBMkMsbUNBQW1DLGtCQUFrQixHQUFHLEdBQUcsZ0NBQWdDLG1CQUFtQixNQUFNLE1BQU0sV0FBVyxxQkFBcUIsUUFBUSxtQkFBbUIsa0dBQWtHLHVEQUF1RCxtQkFBbUIsZUFBZSxrQ0FBa0MseUNBQXlDLFdBQVcsR0FBRyxHQUFHLGVBQWUsa0JBQWtCLGdFQUFnRSw0RUFBNEUsZUFBZSxTQUFTLG1CQUFtQixnQkFBZ0IsbUJBQW1CLHVFQUF1RSxlQUFlLDZGQUE2RixjQUFjLHNGQUFzRixjQUFjLDRGQUE0RixjQUFjLHFDQUFxQyx1QkFBdUIsVUFBVSxLQUFLLEtBQUssc0JBQXNCLFdBQVcsZUFBZSx3RUFBd0UsV0FBVyxVQUFVLDhCQUE4QixPQUFPLGVBQWUsZ0RBQWdELHVCQUF1QixpSkFBaUosR0FBRyxHQUFHLGlEQUFpRCx1REFBdUQsNENBQTRDLEdBQUcsSUFBSSxHQUFHLDRCQUE0QixPQUFPLGlCQUFpQiwwQ0FBMEMsdUJBQXVCLHNCQUFzQiwyQkFBMkIsc0dBQXNHLDBDQUEwQyx1QkFBdUIsZ0RBQWdELHlCQUF5QixpQkFBaUIsNkRBQTZELFdBQVcsR0FBRyxvQkFBb0IsSUFBSSxJQUFJLHNCQUFzQiw0QkFBNEIsbURBQW1ELGNBQWMsZ0JBQWdCLE9BQU8saUlBQWlJLHVCQUF1QixLQUFLLFNBQVMsZUFBZSxpQkFBaUIsZ0VBQWdFLG9DQUFvQyxpQkFBaUIsV0FBVyxjQUFjLElBQUksY0FBYywrRUFBK0Usd0VBQXdFLGNBQWMsZUFBZSx3RUFBd0UsZ0JBQWdCLEdBQUcsNkJBQTZCLE9BQU8sZUFBZSxxQ0FBcUMsK0ZBQStGLGVBQWUsYUFBYSxzQkFBc0IsaURBQWlELGtDQUFrQyxxQkFBcUIsTUFBTSxnR0FBZ0csR0FBRyxHQUFHLDZCQUE2Qix1QkFBdUIsK0ZBQStGLEdBQUcsY0FBYywyQ0FBMkMsY0FBYyxxREFBcUQsNkNBQTZDLEdBQUcsOEJBQThCLGFBQWEsVUFBVSxlQUFlLHlDQUF5Qyx5Q0FBeUMsZ0VBQWdFLEtBQUssT0FBTyxtREFBbUQsb0JBQW9CLHNCQUFzQiwyQkFBMkIscUNBQXFDLGVBQWUsSUFBSSxpQkFBaUIsT0FBTyxlQUFlLE1BQU0sSUFBSSwrQkFBK0IsdUJBQXVCLDJFQUEyRSxtQ0FBbUMsMERBQTBELEdBQUcsVUFBVSxHQUFHLHlEQUF5RCxvRUFBb0UsZ0JBQWdCLGtFQUFrRSxFQUFFLG9JQUFvSSwrQkFBK0IsZ0JBQWdCLG9FQUFvRSxpQkFBaUIsb0RBQW9ELElBQUksR0FBRyxrQkFBa0IsaUJBQWlCLE9BQU8sZUFBZSxxQ0FBcUMsb0JBQW9CLDBGQUEwRixtQ0FBbUMsTUFBTSw4QkFBOEIsb0JBQW9CLDhEQUE4RCxHQUFHLHlDQUF5QywrQkFBK0IsdUJBQXVCLE1BQU0sdUJBQXVCLHVEQUF1RCx1REFBdUQsR0FBRyxJQUFJLEdBQUcsOEJBQThCLG9DQUFvQyxZQUFZLHdFQUF3RSxZQUFZLHNCQUFzQixpQkFBaUIsUUFBUSw4Q0FBOEMsY0FBYyxVQUFVLE1BQU0sc0JBQXNCLG9DQUFvQyw0REFBNEQsa0RBQWtELElBQUksSUFBMlEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXHdlYi12aXRhbHNcXGRpc3RcXHdlYi12aXRhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGUsbix0LGkscixhPS0xLG89ZnVuY3Rpb24oZSl7YWRkRXZlbnRMaXN0ZW5lcihcInBhZ2VzaG93XCIsKGZ1bmN0aW9uKG4pe24ucGVyc2lzdGVkJiYoYT1uLnRpbWVTdGFtcCxlKG4pKX0pLCEwKX0sYz1mdW5jdGlvbigpe3JldHVybiB3aW5kb3cucGVyZm9ybWFuY2UmJnBlcmZvcm1hbmNlLmdldEVudHJpZXNCeVR5cGUmJnBlcmZvcm1hbmNlLmdldEVudHJpZXNCeVR5cGUoXCJuYXZpZ2F0aW9uXCIpWzBdfSx1PWZ1bmN0aW9uKCl7dmFyIGU9YygpO3JldHVybiBlJiZlLmFjdGl2YXRpb25TdGFydHx8MH0sZj1mdW5jdGlvbihlLG4pe3ZhciB0PWMoKSxpPVwibmF2aWdhdGVcIjthPj0wP2k9XCJiYWNrLWZvcndhcmQtY2FjaGVcIjp0JiYoZG9jdW1lbnQucHJlcmVuZGVyaW5nfHx1KCk+MD9pPVwicHJlcmVuZGVyXCI6ZG9jdW1lbnQud2FzRGlzY2FyZGVkP2k9XCJyZXN0b3JlXCI6dC50eXBlJiYoaT10LnR5cGUucmVwbGFjZSgvXy9nLFwiLVwiKSkpO3JldHVybntuYW1lOmUsdmFsdWU6dm9pZCAwPT09bj8tMTpuLHJhdGluZzpcImdvb2RcIixkZWx0YTowLGVudHJpZXM6W10saWQ6XCJ2My1cIi5jb25jYXQoRGF0ZS5ub3coKSxcIi1cIikuY29uY2F0KE1hdGguZmxvb3IoODk5OTk5OTk5OTk5OSpNYXRoLnJhbmRvbSgpKSsxZTEyKSxuYXZpZ2F0aW9uVHlwZTppfX0scz1mdW5jdGlvbihlLG4sdCl7dHJ5e2lmKFBlcmZvcm1hbmNlT2JzZXJ2ZXIuc3VwcG9ydGVkRW50cnlUeXBlcy5pbmNsdWRlcyhlKSl7dmFyIGk9bmV3IFBlcmZvcm1hbmNlT2JzZXJ2ZXIoKGZ1bmN0aW9uKGUpe1Byb21pc2UucmVzb2x2ZSgpLnRoZW4oKGZ1bmN0aW9uKCl7bihlLmdldEVudHJpZXMoKSl9KSl9KSk7cmV0dXJuIGkub2JzZXJ2ZShPYmplY3QuYXNzaWduKHt0eXBlOmUsYnVmZmVyZWQ6ITB9LHR8fHt9KSksaX19Y2F0Y2goZSl7fX0sZD1mdW5jdGlvbihlLG4sdCxpKXt2YXIgcixhO3JldHVybiBmdW5jdGlvbihvKXtuLnZhbHVlPj0wJiYob3x8aSkmJigoYT1uLnZhbHVlLShyfHwwKSl8fHZvaWQgMD09PXIpJiYocj1uLnZhbHVlLG4uZGVsdGE9YSxuLnJhdGluZz1mdW5jdGlvbihlLG4pe3JldHVybiBlPm5bMV0/XCJwb29yXCI6ZT5uWzBdP1wibmVlZHMtaW1wcm92ZW1lbnRcIjpcImdvb2RcIn0obi52YWx1ZSx0KSxlKG4pKX19LGw9ZnVuY3Rpb24oZSl7cmVxdWVzdEFuaW1hdGlvbkZyYW1lKChmdW5jdGlvbigpe3JldHVybiByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKGZ1bmN0aW9uKCl7cmV0dXJuIGUoKX0pKX0pKX0scD1mdW5jdGlvbihlKXt2YXIgbj1mdW5jdGlvbihuKXtcInBhZ2VoaWRlXCIhPT1uLnR5cGUmJlwiaGlkZGVuXCIhPT1kb2N1bWVudC52aXNpYmlsaXR5U3RhdGV8fGUobil9O2FkZEV2ZW50TGlzdGVuZXIoXCJ2aXNpYmlsaXR5Y2hhbmdlXCIsbiwhMCksYWRkRXZlbnRMaXN0ZW5lcihcInBhZ2VoaWRlXCIsbiwhMCl9LHY9ZnVuY3Rpb24oZSl7dmFyIG49ITE7cmV0dXJuIGZ1bmN0aW9uKHQpe258fChlKHQpLG49ITApfX0sbT0tMSxoPWZ1bmN0aW9uKCl7cmV0dXJuXCJoaWRkZW5cIiE9PWRvY3VtZW50LnZpc2liaWxpdHlTdGF0ZXx8ZG9jdW1lbnQucHJlcmVuZGVyaW5nPzEvMDowfSxnPWZ1bmN0aW9uKGUpe1wiaGlkZGVuXCI9PT1kb2N1bWVudC52aXNpYmlsaXR5U3RhdGUmJm0+LTEmJihtPVwidmlzaWJpbGl0eWNoYW5nZVwiPT09ZS50eXBlP2UudGltZVN0YW1wOjAsVCgpKX0seT1mdW5jdGlvbigpe2FkZEV2ZW50TGlzdGVuZXIoXCJ2aXNpYmlsaXR5Y2hhbmdlXCIsZywhMCksYWRkRXZlbnRMaXN0ZW5lcihcInByZXJlbmRlcmluZ2NoYW5nZVwiLGcsITApfSxUPWZ1bmN0aW9uKCl7cmVtb3ZlRXZlbnRMaXN0ZW5lcihcInZpc2liaWxpdHljaGFuZ2VcIixnLCEwKSxyZW1vdmVFdmVudExpc3RlbmVyKFwicHJlcmVuZGVyaW5nY2hhbmdlXCIsZywhMCl9LEU9ZnVuY3Rpb24oKXtyZXR1cm4gbTwwJiYobT1oKCkseSgpLG8oKGZ1bmN0aW9uKCl7c2V0VGltZW91dCgoZnVuY3Rpb24oKXttPWgoKSx5KCl9KSwwKX0pKSkse2dldCBmaXJzdEhpZGRlblRpbWUoKXtyZXR1cm4gbX19fSxDPWZ1bmN0aW9uKGUpe2RvY3VtZW50LnByZXJlbmRlcmluZz9hZGRFdmVudExpc3RlbmVyKFwicHJlcmVuZGVyaW5nY2hhbmdlXCIsKGZ1bmN0aW9uKCl7cmV0dXJuIGUoKX0pLCEwKTplKCl9LEw9WzE4MDAsM2UzXSx3PWZ1bmN0aW9uKGUsbil7bj1ufHx7fSxDKChmdW5jdGlvbigpe3ZhciB0LGk9RSgpLHI9ZihcIkZDUFwiKSxhPXMoXCJwYWludFwiLChmdW5jdGlvbihlKXtlLmZvckVhY2goKGZ1bmN0aW9uKGUpe1wiZmlyc3QtY29udGVudGZ1bC1wYWludFwiPT09ZS5uYW1lJiYoYS5kaXNjb25uZWN0KCksZS5zdGFydFRpbWU8aS5maXJzdEhpZGRlblRpbWUmJihyLnZhbHVlPU1hdGgubWF4KGUuc3RhcnRUaW1lLXUoKSwwKSxyLmVudHJpZXMucHVzaChlKSx0KCEwKSkpfSkpfSkpO2EmJih0PWQoZSxyLEwsbi5yZXBvcnRBbGxDaGFuZ2VzKSxvKChmdW5jdGlvbihpKXtyPWYoXCJGQ1BcIiksdD1kKGUscixMLG4ucmVwb3J0QWxsQ2hhbmdlcyksbCgoZnVuY3Rpb24oKXtyLnZhbHVlPXBlcmZvcm1hbmNlLm5vdygpLWkudGltZVN0YW1wLHQoITApfSkpfSkpKX0pKX0sYj1bLjEsLjI1XSxTPWZ1bmN0aW9uKGUsbil7bj1ufHx7fSx3KHYoKGZ1bmN0aW9uKCl7dmFyIHQsaT1mKFwiQ0xTXCIsMCkscj0wLGE9W10sYz1mdW5jdGlvbihlKXtlLmZvckVhY2goKGZ1bmN0aW9uKGUpe2lmKCFlLmhhZFJlY2VudElucHV0KXt2YXIgbj1hWzBdLHQ9YVthLmxlbmd0aC0xXTtyJiZlLnN0YXJ0VGltZS10LnN0YXJ0VGltZTwxZTMmJmUuc3RhcnRUaW1lLW4uc3RhcnRUaW1lPDVlMz8ocis9ZS52YWx1ZSxhLnB1c2goZSkpOihyPWUudmFsdWUsYT1bZV0pfX0pKSxyPmkudmFsdWUmJihpLnZhbHVlPXIsaS5lbnRyaWVzPWEsdCgpKX0sdT1zKFwibGF5b3V0LXNoaWZ0XCIsYyk7dSYmKHQ9ZChlLGksYixuLnJlcG9ydEFsbENoYW5nZXMpLHAoKGZ1bmN0aW9uKCl7Yyh1LnRha2VSZWNvcmRzKCkpLHQoITApfSkpLG8oKGZ1bmN0aW9uKCl7cj0wLGk9ZihcIkNMU1wiLDApLHQ9ZChlLGksYixuLnJlcG9ydEFsbENoYW5nZXMpLGwoKGZ1bmN0aW9uKCl7cmV0dXJuIHQoKX0pKX0pKSxzZXRUaW1lb3V0KHQsMCkpfSkpKX0sQT17cGFzc2l2ZTohMCxjYXB0dXJlOiEwfSxJPW5ldyBEYXRlLFA9ZnVuY3Rpb24oaSxyKXtlfHwoZT1yLG49aSx0PW5ldyBEYXRlLGsocmVtb3ZlRXZlbnRMaXN0ZW5lciksRigpKX0sRj1mdW5jdGlvbigpe2lmKG4+PTAmJm48dC1JKXt2YXIgcj17ZW50cnlUeXBlOlwiZmlyc3QtaW5wdXRcIixuYW1lOmUudHlwZSx0YXJnZXQ6ZS50YXJnZXQsY2FuY2VsYWJsZTplLmNhbmNlbGFibGUsc3RhcnRUaW1lOmUudGltZVN0YW1wLHByb2Nlc3NpbmdTdGFydDplLnRpbWVTdGFtcCtufTtpLmZvckVhY2goKGZ1bmN0aW9uKGUpe2Uocil9KSksaT1bXX19LE09ZnVuY3Rpb24oZSl7aWYoZS5jYW5jZWxhYmxlKXt2YXIgbj0oZS50aW1lU3RhbXA+MWUxMj9uZXcgRGF0ZTpwZXJmb3JtYW5jZS5ub3coKSktZS50aW1lU3RhbXA7XCJwb2ludGVyZG93blwiPT1lLnR5cGU/ZnVuY3Rpb24oZSxuKXt2YXIgdD1mdW5jdGlvbigpe1AoZSxuKSxyKCl9LGk9ZnVuY3Rpb24oKXtyKCl9LHI9ZnVuY3Rpb24oKXtyZW1vdmVFdmVudExpc3RlbmVyKFwicG9pbnRlcnVwXCIsdCxBKSxyZW1vdmVFdmVudExpc3RlbmVyKFwicG9pbnRlcmNhbmNlbFwiLGksQSl9O2FkZEV2ZW50TGlzdGVuZXIoXCJwb2ludGVydXBcIix0LEEpLGFkZEV2ZW50TGlzdGVuZXIoXCJwb2ludGVyY2FuY2VsXCIsaSxBKX0obixlKTpQKG4sZSl9fSxrPWZ1bmN0aW9uKGUpe1tcIm1vdXNlZG93blwiLFwia2V5ZG93blwiLFwidG91Y2hzdGFydFwiLFwicG9pbnRlcmRvd25cIl0uZm9yRWFjaCgoZnVuY3Rpb24obil7cmV0dXJuIGUobixNLEEpfSkpfSxEPVsxMDAsMzAwXSx4PWZ1bmN0aW9uKHQscil7cj1yfHx7fSxDKChmdW5jdGlvbigpe3ZhciBhLGM9RSgpLHU9ZihcIkZJRFwiKSxsPWZ1bmN0aW9uKGUpe2Uuc3RhcnRUaW1lPGMuZmlyc3RIaWRkZW5UaW1lJiYodS52YWx1ZT1lLnByb2Nlc3NpbmdTdGFydC1lLnN0YXJ0VGltZSx1LmVudHJpZXMucHVzaChlKSxhKCEwKSl9LG09ZnVuY3Rpb24oZSl7ZS5mb3JFYWNoKGwpfSxoPXMoXCJmaXJzdC1pbnB1dFwiLG0pO2E9ZCh0LHUsRCxyLnJlcG9ydEFsbENoYW5nZXMpLGgmJnAodigoZnVuY3Rpb24oKXttKGgudGFrZVJlY29yZHMoKSksaC5kaXNjb25uZWN0KCl9KSkpLGgmJm8oKGZ1bmN0aW9uKCl7dmFyIG87dT1mKFwiRklEXCIpLGE9ZCh0LHUsRCxyLnJlcG9ydEFsbENoYW5nZXMpLGk9W10sbj0tMSxlPW51bGwsayhhZGRFdmVudExpc3RlbmVyKSxvPWwsaS5wdXNoKG8pLEYoKX0pKX0pKX0sQj0wLFI9MS8wLEg9MCxOPWZ1bmN0aW9uKGUpe2UuZm9yRWFjaCgoZnVuY3Rpb24oZSl7ZS5pbnRlcmFjdGlvbklkJiYoUj1NYXRoLm1pbihSLGUuaW50ZXJhY3Rpb25JZCksSD1NYXRoLm1heChILGUuaW50ZXJhY3Rpb25JZCksQj1IPyhILVIpLzcrMTowKX0pKX0sTz1mdW5jdGlvbigpe3JldHVybiByP0I6cGVyZm9ybWFuY2UuaW50ZXJhY3Rpb25Db3VudHx8MH0scT1mdW5jdGlvbigpe1wiaW50ZXJhY3Rpb25Db3VudFwiaW4gcGVyZm9ybWFuY2V8fHJ8fChyPXMoXCJldmVudFwiLE4se3R5cGU6XCJldmVudFwiLGJ1ZmZlcmVkOiEwLGR1cmF0aW9uVGhyZXNob2xkOjB9KSl9LGo9WzIwMCw1MDBdLF89MCx6PWZ1bmN0aW9uKCl7cmV0dXJuIE8oKS1ffSxHPVtdLEo9e30sSz1mdW5jdGlvbihlKXt2YXIgbj1HW0cubGVuZ3RoLTFdLHQ9SltlLmludGVyYWN0aW9uSWRdO2lmKHR8fEcubGVuZ3RoPDEwfHxlLmR1cmF0aW9uPm4ubGF0ZW5jeSl7aWYodCl0LmVudHJpZXMucHVzaChlKSx0LmxhdGVuY3k9TWF0aC5tYXgodC5sYXRlbmN5LGUuZHVyYXRpb24pO2Vsc2V7dmFyIGk9e2lkOmUuaW50ZXJhY3Rpb25JZCxsYXRlbmN5OmUuZHVyYXRpb24sZW50cmllczpbZV19O0pbaS5pZF09aSxHLnB1c2goaSl9Ry5zb3J0KChmdW5jdGlvbihlLG4pe3JldHVybiBuLmxhdGVuY3ktZS5sYXRlbmN5fSkpLEcuc3BsaWNlKDEwKS5mb3JFYWNoKChmdW5jdGlvbihlKXtkZWxldGUgSltlLmlkXX0pKX19LFE9ZnVuY3Rpb24oZSxuKXtuPW58fHt9LEMoKGZ1bmN0aW9uKCl7dmFyIHQ7cSgpO3ZhciBpLHI9ZihcIklOUFwiKSxhPWZ1bmN0aW9uKGUpe2UuZm9yRWFjaCgoZnVuY3Rpb24oZSl7KGUuaW50ZXJhY3Rpb25JZCYmSyhlKSxcImZpcnN0LWlucHV0XCI9PT1lLmVudHJ5VHlwZSkmJighRy5zb21lKChmdW5jdGlvbihuKXtyZXR1cm4gbi5lbnRyaWVzLnNvbWUoKGZ1bmN0aW9uKG4pe3JldHVybiBlLmR1cmF0aW9uPT09bi5kdXJhdGlvbiYmZS5zdGFydFRpbWU9PT1uLnN0YXJ0VGltZX0pKX0pKSYmSyhlKSl9KSk7dmFyIG4sdD0obj1NYXRoLm1pbihHLmxlbmd0aC0xLE1hdGguZmxvb3IoeigpLzUwKSksR1tuXSk7dCYmdC5sYXRlbmN5IT09ci52YWx1ZSYmKHIudmFsdWU9dC5sYXRlbmN5LHIuZW50cmllcz10LmVudHJpZXMsaSgpKX0sYz1zKFwiZXZlbnRcIixhLHtkdXJhdGlvblRocmVzaG9sZDpudWxsIT09KHQ9bi5kdXJhdGlvblRocmVzaG9sZCkmJnZvaWQgMCE9PXQ/dDo0MH0pO2k9ZChlLHIsaixuLnJlcG9ydEFsbENoYW5nZXMpLGMmJihcIlBlcmZvcm1hbmNlRXZlbnRUaW1pbmdcImluIHdpbmRvdyYmXCJpbnRlcmFjdGlvbklkXCJpbiBQZXJmb3JtYW5jZUV2ZW50VGltaW5nLnByb3RvdHlwZSYmYy5vYnNlcnZlKHt0eXBlOlwiZmlyc3QtaW5wdXRcIixidWZmZXJlZDohMH0pLHAoKGZ1bmN0aW9uKCl7YShjLnRha2VSZWNvcmRzKCkpLHIudmFsdWU8MCYmeigpPjAmJihyLnZhbHVlPTAsci5lbnRyaWVzPVtdKSxpKCEwKX0pKSxvKChmdW5jdGlvbigpe0c9W10sXz1PKCkscj1mKFwiSU5QXCIpLGk9ZChlLHIsaixuLnJlcG9ydEFsbENoYW5nZXMpfSkpKX0pKX0sVT1bMjUwMCw0ZTNdLFY9e30sVz1mdW5jdGlvbihlLG4pe249bnx8e30sQygoZnVuY3Rpb24oKXt2YXIgdCxpPUUoKSxyPWYoXCJMQ1BcIiksYT1mdW5jdGlvbihlKXt2YXIgbj1lW2UubGVuZ3RoLTFdO24mJm4uc3RhcnRUaW1lPGkuZmlyc3RIaWRkZW5UaW1lJiYoci52YWx1ZT1NYXRoLm1heChuLnN0YXJ0VGltZS11KCksMCksci5lbnRyaWVzPVtuXSx0KCkpfSxjPXMoXCJsYXJnZXN0LWNvbnRlbnRmdWwtcGFpbnRcIixhKTtpZihjKXt0PWQoZSxyLFUsbi5yZXBvcnRBbGxDaGFuZ2VzKTt2YXIgbT12KChmdW5jdGlvbigpe1Zbci5pZF18fChhKGMudGFrZVJlY29yZHMoKSksYy5kaXNjb25uZWN0KCksVltyLmlkXT0hMCx0KCEwKSl9KSk7W1wia2V5ZG93blwiLFwiY2xpY2tcIl0uZm9yRWFjaCgoZnVuY3Rpb24oZSl7YWRkRXZlbnRMaXN0ZW5lcihlLChmdW5jdGlvbigpe3JldHVybiBzZXRUaW1lb3V0KG0sMCl9KSwhMCl9KSkscChtKSxvKChmdW5jdGlvbihpKXtyPWYoXCJMQ1BcIiksdD1kKGUscixVLG4ucmVwb3J0QWxsQ2hhbmdlcyksbCgoZnVuY3Rpb24oKXtyLnZhbHVlPXBlcmZvcm1hbmNlLm5vdygpLWkudGltZVN0YW1wLFZbci5pZF09ITAsdCghMCl9KSl9KSl9fSkpfSxYPVs4MDAsMTgwMF0sWT1mdW5jdGlvbiBlKG4pe2RvY3VtZW50LnByZXJlbmRlcmluZz9DKChmdW5jdGlvbigpe3JldHVybiBlKG4pfSkpOlwiY29tcGxldGVcIiE9PWRvY3VtZW50LnJlYWR5U3RhdGU/YWRkRXZlbnRMaXN0ZW5lcihcImxvYWRcIiwoZnVuY3Rpb24oKXtyZXR1cm4gZShuKX0pLCEwKTpzZXRUaW1lb3V0KG4sMCl9LFo9ZnVuY3Rpb24oZSxuKXtuPW58fHt9O3ZhciB0PWYoXCJUVEZCXCIpLGk9ZChlLHQsWCxuLnJlcG9ydEFsbENoYW5nZXMpO1koKGZ1bmN0aW9uKCl7dmFyIHI9YygpO2lmKHIpe3ZhciBhPXIucmVzcG9uc2VTdGFydDtpZihhPD0wfHxhPnBlcmZvcm1hbmNlLm5vdygpKXJldHVybjt0LnZhbHVlPU1hdGgubWF4KGEtdSgpLDApLHQuZW50cmllcz1bcl0saSghMCksbygoZnVuY3Rpb24oKXt0PWYoXCJUVEZCXCIsMCksKGk9ZChlLHQsWCxuLnJlcG9ydEFsbENoYW5nZXMpKSghMCl9KSl9fSkpfTtleHBvcnR7YiBhcyBDTFNUaHJlc2hvbGRzLEwgYXMgRkNQVGhyZXNob2xkcyxEIGFzIEZJRFRocmVzaG9sZHMsaiBhcyBJTlBUaHJlc2hvbGRzLFUgYXMgTENQVGhyZXNob2xkcyxYIGFzIFRURkJUaHJlc2hvbGRzLFMgYXMgZ2V0Q0xTLHcgYXMgZ2V0RkNQLHggYXMgZ2V0RklELFEgYXMgZ2V0SU5QLFcgYXMgZ2V0TENQLFogYXMgZ2V0VFRGQixTIGFzIG9uQ0xTLHcgYXMgb25GQ1AseCBhcyBvbkZJRCxRIGFzIG9uSU5QLFcgYXMgb25MQ1AsWiBhcyBvblRURkJ9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/web-vitals/dist/web-vitals.js\n");

/***/ })

};
;