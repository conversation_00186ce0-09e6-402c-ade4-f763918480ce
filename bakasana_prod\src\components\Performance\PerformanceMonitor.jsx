'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';

export default function PerformanceMonitor() {
  const pathname = usePathname();
  const metricsRef = useRef({});
  const observerRef = useRef(null);

  useEffect(() => {
    // Monitor Core Web Vitals
    const trackWebVitals = async () => {
      try {
        const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');
        
        getCLS((metric) => {
          metricsRef.current.cls = metric;
          trackMetric('CLS', metric);
        });

        getFID((metric) => {
          metricsRef.current.fid = metric;
          trackMetric('FID', metric);
        });

        getFCP((metric) => {
          metricsRef.current.fcp = metric;
          trackMetric('FCP', metric);
        });

        getLCP((metric) => {
          metricsRef.current.lcp = metric;
          trackMetric('LCP', metric);
        });

        getTTFB((metric) => {
          metricsRef.current.ttfb = metric;
          trackMetric('TTFB', metric);
        });
      } catch (error) {
        console.error('Error loading web-vitals:', error);
      }
    };

    trackWebVitals();

    // Monitor Resource Loading
    const trackResourceLoading = () => {
      if ('performance' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              trackResourceMetric(entry);
            }
          }
        });
        observer.observe({ entryTypes: ['resource'] });
        observerRef.current = observer;
      }
    };

    trackResourceLoading();

    // Monitor Memory Usage
    const trackMemoryUsage = () => {
      if ('memory' in performance) {
        const memoryInfo = performance.memory;
        if (window.gtag) {
          window.gtag('event', 'memory_usage', {
            event_category: 'Performance',
            event_label: pathname,
            custom_parameter_1: Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024),
            custom_parameter_2: Math.round(memoryInfo.totalJSHeapSize / 1024 / 1024)
          });
        }
      }
    };

    // Track memory every 30 seconds
    const memoryInterval = setInterval(trackMemoryUsage, 30000);

    // Monitor Network Information
    const trackNetworkInfo = () => {
      if ('connection' in navigator) {
        const connection = navigator.connection;
        if (window.gtag) {
          window.gtag('event', 'network_info', {
            event_category: 'Performance',
            event_label: pathname,
            custom_parameter_1: connection.effectiveType,
            custom_parameter_2: connection.downlink
          });
        }
      }
    };

    trackNetworkInfo();

    // Monitor User Experience Metrics
    const trackUserExperience = () => {
      let scrollDepth = 0;
      let maxScrollDepth = 0;
      let timeOnPage = Date.now();
      let interactions = 0;

      const handleScroll = () => {
        const scrollPercentage = Math.round(
          (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        );
        scrollDepth = scrollPercentage;
        maxScrollDepth = Math.max(maxScrollDepth, scrollPercentage);
      };

      const handleInteraction = () => {
        interactions++;
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('click', handleInteraction);
      window.addEventListener('keydown', handleInteraction);

      // Track user experience metrics before page unload
      const handleBeforeUnload = () => {
        const sessionDuration = Date.now() - timeOnPage;
        if (window.gtag) {
          window.gtag('event', 'user_experience', {
            event_category: 'Engagement',
            event_label: pathname,
            custom_parameter_1: Math.round(sessionDuration / 1000),
            custom_parameter_2: maxScrollDepth,
            value: interactions
          });
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        window.removeEventListener('scroll', handleScroll);
        window.removeEventListener('click', handleInteraction);
        window.removeEventListener('keydown', handleInteraction);
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    };

    const cleanupUserExperience = trackUserExperience();

    // Monitor Page Load Performance
    const trackPageLoadPerformance = () => {
      if ('performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation && window.gtag) {
          window.gtag('event', 'page_load_performance', {
            event_category: 'Performance',
            event_label: pathname,
            custom_parameter_1: Math.round(navigation.loadEventEnd - navigation.loadEventStart),
            custom_parameter_2: Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart),
            value: Math.round(navigation.loadEventEnd - navigation.fetchStart)
          });
        }
      }
    };

    // Track page load performance after a short delay
    setTimeout(trackPageLoadPerformance, 1000);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      clearInterval(memoryInterval);
      if (cleanupUserExperience) {
        cleanupUserExperience();
      }
    };
  }, [pathname]);

  const trackMetric = (metricName, metric) => {
    if (window.gtag) {
      let value = metric.value;
      let category = 'Performance';
      
      // Convert metric value to appropriate format
      if (metricName === 'CLS') {
        value = Math.round(value * 1000);
        category = 'Core Web Vitals';
      } else if (metricName === 'FID') {
        value = Math.round(value);
        category = 'Core Web Vitals';
      } else if (metricName === 'FCP' || metricName === 'LCP' || metricName === 'TTFB') {
        value = Math.round(value);
        category = 'Core Web Vitals';
      }

      window.gtag('event', `web_vital_${metricName.toLowerCase()}`, {
        event_category: category,
        event_label: pathname,
        value: value,
        custom_parameter_1: metric.rating || 'unknown',
        custom_parameter_2: metric.delta || 0
      });

      // Track performance thresholds
      const thresholds = {
        CLS: { good: 0.1, poor: 0.25 },
        FID: { good: 100, poor: 300 },
        FCP: { good: 1800, poor: 3000 },
        LCP: { good: 2500, poor: 4000 },
        TTFB: { good: 800, poor: 1800 }
      };

      const threshold = thresholds[metricName];
      if (threshold) {
        let rating = 'good';
        if (metric.value > threshold.poor) {
          rating = 'poor';
        } else if (metric.value > threshold.good) {
          rating = 'needs-improvement';
        }

        window.gtag('event', 'performance_threshold', {
          event_category: 'Performance Monitoring',
          event_label: `${metricName}_${rating}`,
          custom_parameter_1: metricName,
          custom_parameter_2: rating,
          value: value
        });
      }
    }
  };

  const trackResourceMetric = (entry) => {
    if (window.gtag) {
      const resourceType = entry.initiatorType || 'other';
      const duration = Math.round(entry.duration);
      const size = entry.transferSize || 0;

      window.gtag('event', 'resource_performance', {
        event_category: 'Resource Loading',
        event_label: `${resourceType}_${entry.name.split('/').pop()}`,
        custom_parameter_1: resourceType,
        custom_parameter_2: duration,
        value: size
      });

      // Track slow resources
      if (duration > 1000) {
        window.gtag('event', 'slow_resource', {
          event_category: 'Performance Issues',
          event_label: entry.name,
          custom_parameter_1: resourceType,
          custom_parameter_2: duration,
          value: size
        });
      }
    }
  };

  // Monitor JavaScript errors
  useEffect(() => {
    const handleError = (error) => {
      if (window.gtag) {
        window.gtag('event', 'javascript_error', {
          event_category: 'Error',
          event_label: error.message,
          custom_parameter_1: error.filename || 'unknown',
          custom_parameter_2: error.lineno || 0,
          value: 1
        });
      }
    };

    const handleUnhandledRejection = (event) => {
      if (window.gtag) {
        window.gtag('event', 'unhandled_promise_rejection', {
          event_category: 'Error',
          event_label: event.reason?.message || 'Unknown promise rejection',
          custom_parameter_1: event.reason?.stack || 'No stack trace',
          value: 1
        });
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return null;
}